# EMA反转做空策略配置文件
strategy:
  ema-reversal-short:
    # ========== 基础配置 ==========
    enabled: true                    # 是否启用策略
    inst-id-limit: 20               # 交易对数量限制
    processing-interval: 1000       # 处理间隔时间（毫秒）
    enable-detailed-logging: false  # 是否启用详细日志
    
    # ========== 历史趋势确认参数 ==========
    history-period: 100             # 历史K线检查周期
    consecutive-down-bars: 40       # 连续下跌K线数量要求
    ema-period: 55                  # EMA周期
    
    # ========== 反弹监控参数 ==========
    rebound-time-window: 16         # 反弹监控时间窗口（4小时 = 16个15分钟K线）
    min-rebound-percent: 0.005      # 反弹确认的最小涨幅百分比（0.5%）
    
    # ========== ADX确认参数 ==========
    adx-period: 14                  # ADX计算周期
    adx-threshold: 55.0             # ADX强度阈值
    adx-check-period: 5             # ADX检查的K线数量
    
    # ========== 风险管理参数 ==========
    atr-period: 14                  # ATR计算周期
    stop-loss-multiplier: 4.0       # 止损倍数
    take-profit-multiplier: 16.0    # 止盈倍数
    
    # ========== 仓位管理参数 ==========
    position-size-percent: 0.02     # 仓位大小百分比（基于账户资金）
    max-daily-trades: 3             # 每日最大交易次数
    
    # ========== 过滤器参数 ==========
    enable-volume-filter: true      # 是否启用成交量过滤
    volume-multiplier: 0.8          # 成交量倍数要求
    enable-volatility-filter: true  # 是否启用波动率过滤
    max-volatility-multiplier: 2.0  # 最大波动率倍数
    
    # ========== 时间过滤参数 ==========
    enable-time-filter: false       # 是否启用时间过滤
    forbidden-hours:                # 禁止交易的小时（24小时制）
      - 0
      - 1
      - 2
      - 3
      - 4
      - 5
    
    # ========== 风险控制参数 ==========
    max-drawdown-percent: 0.10      # 最大回撤百分比（10%）
    max-consecutive-losses: 3       # 连续亏损次数限制
    pause-after-losses-minutes: 60  # 连续亏损后暂停时间（分钟）
    
    # ========== 监控和通知参数 ==========
    enable-performance-monitoring: true  # 是否启用性能监控
    enable-push-notification: true       # 是否启用推送通知
    min-notification-amount: 10.0        # 推送通知的最小盈亏金额
    
    # ========== 调试参数 ==========
    simulation-mode: false          # 是否启用模拟模式（不实际下单）
    save-signals-to-redis: true     # 是否保存交易信号到Redis
    signal-expiration-hours: 24     # 信号保存的过期时间（小时）

# 日志配置
logging:
  level:
    com.miner.strategy.EMAReversalShortStrategy: INFO
    
# 如果需要单独运行此策略，可以设置定时任务
# 注意：这需要在主配置文件中启用相应的定时任务配置
