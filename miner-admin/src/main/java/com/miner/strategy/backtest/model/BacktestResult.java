package com.miner.strategy.backtest.model;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 回测结果模型
 * 保存回测结果和统计数据
 * <AUTHOR>
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class BacktestResult {
    // 回测基本信息
    private String strategyName;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private List<String> symbols;
    private double initialCapital;
    private double finalCapital;

    // 交易统计
    private int totalTrades;
    private int winningTrades;
    private int losingTrades;
    private double winRate;

    // 盈亏统计
    private BigDecimal totalProfit;
    private BigDecimal totalLoss;
    private BigDecimal netProfit;
    private double profitFactor;
    private double returnRate; // 年化收益率

    // 风险统计
    private double maxDrawdown;
    private LocalDateTime maxDrawdownStartDate;
    private LocalDateTime maxDrawdownEndDate;
    private double sharpeRatio; // 夏普比率
    private double sortinoRatio; // 索提诺比率

    // 交易记录
    private List<BacktestPosition> positions;

    // 权益曲线数据
    private Map<LocalDateTime, Double> equityCurve;

    // 回撤曲线数据
    private Map<LocalDateTime, Double> drawdownCurve;

    // 月度/年度收益数据
    private Map<String, Double> monthlyReturns;
    private Map<String, Double> yearlyReturns;

    // 参数设置
    private String parameterSettings;

    /**
     * 创建简单汇总信息，方便在控制台打印
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("回测结果总结:\n");
        summary.append(String.format("初始资金: %.2f USDT\n", initialCapital));
        summary.append(String.format("最终资金: %.2f USDT\n", finalCapital));
        summary.append(String.format("收益率: %.2f%%\n", returnRate * 100));
        summary.append(String.format("胜率: %.2f%%\n", winRate * 100));
        summary.append(String.format("总交易次数: %d\n", totalTrades));
        summary.append(String.format("盈利交易: %d\n", winningTrades));
        summary.append(String.format("亏损交易: %d\n", losingTrades));
        summary.append(String.format("最大回撤: %.2f%%\n", maxDrawdown * 100));
        summary.append(String.format("盈亏比: %.2f\n", profitFactor));
        summary.append(String.format("夏普比率: %.2f\n", sharpeRatio));
        return summary.toString();
    }
}
