package com.miner.strategy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.miner.system.okx.bean.trade.param.CancelOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ClearNotDealStrategy extends BaseStrategy {
    @Override
    public void run() {

        JSONObject swap = tradeAPIService.getOrderList("SWAP", null, null, null, null, null, null, null);
        final JSONArray data = swap.getJSONArray("data");
        if (data.isEmpty()) {
            return;
        }
        for (int i = 0; i < data.size(); i++) {
            JSONObject item = data.getJSONObject(i);
            Long cTime = item.getLong("cTime");
            String instId = item.getString("instId");
            String ordId = item.getString("ordId");
            int limit = 60000 * 28;
//            if (btcEth.contains(instId)) {
//                continue;
//            }
            if (System.currentTimeMillis() - cTime > limit) {
                //撤单
                final CancelOrder cancelOrder = new CancelOrder();
                cancelOrder.setInstId(instId);
                cancelOrder.setOrdId(ordId);
                final JSONObject jsonObject = tradeAPIService.cancelOrder(cancelOrder);
                log.info("撤单{} ,结果 ： {}", instId, jsonObject.toJSONString());
            }
        }
    }
}
