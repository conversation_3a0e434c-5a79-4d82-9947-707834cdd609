package com.miner.strategy;

import cn.hutool.core.thread.ThreadUtil;
import com.miner.strategy.config.EMAChannelConfig;
import com.miner.strategy.exception.StrategyExceptionHandler;
import com.miner.strategy.monitor.StrategyPerformanceMonitor;
import com.miner.strategy.util.EMASignalUtil;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

import static com.miner.strategy.util.EMASignalUtil.calculateATR;

/**
 * EMA通道策略（优化版）
 * 在识别出由EMA(55)连续移动形成的趋势后，等待价格回调至动态通道内寻找入场机会。
 *
 * 优化内容：
 * 1. 参数配置化 - 支持动态调整
 * 2. 动态通道宽度 - 基于ATR自适应
 * 3. 技术指标确认 - RSI/MACD多重确认
 * 4. 分批止盈策略 - 提升收益率
 * 5. 市场环境过滤 - 提高稳定性
 * 6. 增强成交量过滤 - 提升信号质量
 * 7. 性能监控 - 实时统计
 *
 * <AUTHOR>
 */
@Component("emaChannelStrategyOptimized")
@Slf4j
public class EMAChannelStrategyOptimized extends BaseStrategy {

    @Resource
    private EMAChannelConfig config;

    @Resource
    private StrategyPerformanceMonitor performanceMonitor;

    @Resource
    private StrategyExceptionHandler exceptionHandler;

    private static final String STRATEGY_NAME = "EMAChannelStrategyOptimized";

    @Override
    public void run() {
        log.info("==== EMA通道策略（优化版）开始执行 ====");

        // 启动性能监控
        if (config.isEnablePerformanceMonitoring()) {
            performanceMonitor.startMonitoring(STRATEGY_NAME);
        }

        // 获取交易对列表
        List<String> instIds = getInstIds(config.getInstIdLimit());
        if (instIds.isEmpty()) {
            log.warn("未获取到交易对列表，策略结束");
            return;
        }

        log.info("获取到{}个交易对，开始处理", instIds.size());

        // 处理每个交易对
        for (String instId : instIds) {
            long processStartTime = 0;
            try {
                if (config.isEnablePerformanceMonitoring()) {
                    processStartTime = performanceMonitor.recordProcessStart(STRATEGY_NAME);
                }

                logProgress(instId);
                boolean hasSignal = processInstrument(instId);

                if (config.isEnablePerformanceMonitoring()) {
                    performanceMonitor.recordProcessComplete(STRATEGY_NAME, processStartTime, hasSignal);
                }

                ThreadUtil.sleep(config.getProcessingInterval());
            } catch (Exception e) {
                log.error("EMA通道策略处理交易对 {} 异常", instId, e);
                if (config.isEnablePerformanceMonitoring() && processStartTime > 0) {
                    performanceMonitor.recordProcessComplete(STRATEGY_NAME, processStartTime, false);
                }
            }
        }

        logFinalStats();
    }

    private void logProgress(String instId) {
        if (config.isEnableDetailedLogging()) {
            StrategyPerformanceMonitor.StrategyStats stats = performanceMonitor.getStats(STRATEGY_NAME);
            int processed = stats != null ? stats.getTotalProcessed().get() : 0;
            log.debug("正在处理交易对: {}, 进度: {}/{}", instId, processed + 1, config.getInstIdLimit());
        }
    }

    private void logFinalStats() {
        log.info("==== EMA通道策略执行完毕 ====");

        if (config.isEnablePerformanceMonitoring()) {
            performanceMonitor.printPerformanceReport(STRATEGY_NAME);
        } else {
            log.info("性能监控已禁用，无详细统计信息");
        }
    }

    private boolean processInstrument(String instId) {
        // 检查是否已有仓位
        if (hasExistingTrade(instId)) {
            return false;
        }

        // 获取K线数据
        List<KLineEntity> klineData = getKlineData(instId);
        if (klineData == null) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = extractClosePrices(klineData);

        // 判断趋势
        String trendDirection = checkEmaTrend(closePrices);
        if ("none".equals(trendDirection)) {
            if (config.isEnablePerformanceMonitoring()) {
                performanceMonitor.recordFilterReject(STRATEGY_NAME, StrategyPerformanceMonitor.FilterType.TREND);
            }
            return false;
        }

        logTrendDetection(instId, trendDirection);

        // 处理入场
        return handleEntry(instId, trendDirection, klineData, closePrices);
    }

    private boolean hasExistingTrade(String instId) {
        if (checkTrade(config.getStrategyKeyPrefix() + instId)) {
            logDebug("[{}] 已有交易记录，跳过处理", instId);
            return true;
        }
        return false;
    }

    private List<KLineEntity> getKlineData(String instId) {
        return exceptionHandler.executeWithExceptionHandling(
            "getKlineData_" + instId,
            () -> {
                logDebug("[{}] 获取{}周期K线数据", instId, config.getMainTimeframe());

                List<KLineEntity> klineData = getKline(instId, config.getMainTimeframe(), config.getKlineLimit());

                if (klineData == null || klineData.size() < config.getMinKlineCount()) {
                    logDebug("[{}] K线数据不足，获取到{}根，需要至少{}根",
                        instId, klineData == null ? 0 : klineData.size(), config.getMinKlineCount());
                    return null;
                }

                return klineData;
            }
        );
    }

    private List<Double> extractClosePrices(List<KLineEntity> klineData) {
        return klineData.stream()
            .map(k -> k.getClosePrice().doubleValue())
            .collect(Collectors.toList());
    }

    private void logTrendDetection(String instId, String trendDirection) {
        if (config.isEnableDetailedLogging()) {
            String trendType = "long".equals(trendDirection) ? "多头" : "空头";
            log.debug("[{}] 检测到连续 {} 根K线EMA{}{}趋势",
                instId, config.getTrendLookbackPeriod(), config.getEmaSlow(), trendType);
        }
    }

    /**
     * 统一处理入场逻辑
     */
    private boolean handleEntry(String instId, String side, List<KLineEntity> kline, List<Double> closePrices) {
        boolean isLong = "long".equals(side);

        // 计算EMA指标
        double emaMid = calculateEMA(closePrices, 0, config.getEmaMid());
        double emaSlow = calculateEMA(closePrices, 0, config.getEmaSlow());
        double currentPrice = kline.get(0).getClosePrice().doubleValue();

        logIndicatorResults(instId, currentPrice, emaMid, emaSlow);

        // 执行过滤器检查
        if (!passAllFilters(instId, kline, isLong)) {
            return false;
        }

        // 检查通道条件
        if (!isPriceInChannel(instId, currentPrice, emaMid, emaSlow, isLong, kline)) {
            return false;
        }

        log.info("[{}] 价格进入{}通道，所有条件满足，准备入场", instId, isLong ? "做多" : "做空");
        processEntry(instId, side, BigDecimal.valueOf(currentPrice), kline);
        return true;
    }

    private void logIndicatorResults(String instId, double currentPrice, double emaMid, double emaSlow) {
        if (config.isEnableDetailedLogging()) {
            log.debug("[{}] 计算指标结果 - 当前价格: {}, EMA{}: {}, EMA{}: {}",
                instId, String.format("%.4f", currentPrice),
                config.getEmaMid(), String.format("%.4f", emaMid),
                config.getEmaSlow(), String.format("%.4f", emaSlow));
        }
    }

    private boolean passAllFilters(String instId, List<KLineEntity> kline, boolean isLong) {
        // 市场环境过滤器 - 暂时禁用波动率检查
        // if (!isMarketEnvironmentSuitable(kline)) {
        //     logDebug("[{}] 市场环境不适合交易，取消{}交易", instId, isLong ? "做多" : "做空");
        //     if (config.isEnablePerformanceMonitoring()) {
        //         performanceMonitor.recordFilterReject(STRATEGY_NAME, StrategyPerformanceMonitor.FilterType.MARKET_ENVIRONMENT);
        //     }
        //     return false;
        // }
        logDebug("[{}] 市场环境过滤器已禁用，跳过检查", instId);

        // 成交量过滤器
        if (!isVolumeSufficient(kline)) {
            logDebug("[{}] 成交量不足，取消{}交易", instId, isLong ? "做多" : "做空");
            if (config.isEnablePerformanceMonitoring()) {
                performanceMonitor.recordFilterReject(STRATEGY_NAME, StrategyPerformanceMonitor.FilterType.VOLUME);
            }
            return false;
        }

        // 技术指标确认（做多和做空都进行确认）
        if (config.isEnableTechnicalConfirm()) {
            if (!confirmTechnicalSignals(instId, kline, isLong)) {
                if (config.isEnablePerformanceMonitoring()) {
                    performanceMonitor.recordFilterReject(STRATEGY_NAME, StrategyPerformanceMonitor.FilterType.TECHNICAL);
                }
                return false;
            }
        }

        return true;
    }

    private boolean confirmTechnicalSignals(String instId, List<KLineEntity> kline, boolean isLong) {
        logDebug("[{}] 开始{}技术指标确认...", instId, isLong ? "做多" : "做空");

        if (isLong) {
            // 做多信号确认：RSI超卖回升 + MACD金叉等
            boolean technicalConfirm = EMASignalUtil.confirmEntrySignal(
                kline,
                config.getRsiPeriod(),
                config.getRsiOversold(),
                config.getRsiRising(),
                config.getEntryScoreThreshold()
            );

            if (!technicalConfirm) {
                logDebug("[{}] 做多技术指标确认失败", instId);
                return false;
            }
        } else {
            // 做空信号确认：RSI超买回落 + MACD死叉等
            boolean technicalConfirm = confirmShortTechnicalSignals(kline);

            if (!technicalConfirm) {
                logDebug("[{}] 做空技术指标确认失败", instId);
                return false;
            }
        }

        logDebug("[{}] {}技术指标确认通过", instId, isLong ? "做多" : "做空");
        return true;
    }

    /**
     * 做空技术指标确认
     */
    private boolean confirmShortTechnicalSignals(List<KLineEntity> kline) {
        try {
            // 检查RSI是否从超买区域回落
            boolean rsiConfirm = checkRSIForShort(kline);

            // 检查MACD是否显示空头信号
            boolean macdConfirm = checkMACDForShort(kline);

            // 检查价格动量
            boolean momentumConfirm = checkMomentumForShort(kline);

            // 至少需要2个指标确认
            int confirmCount = 0;
            if (rsiConfirm) confirmCount++;
            if (macdConfirm) confirmCount++;
            if (momentumConfirm) confirmCount++;

            boolean result = confirmCount >= 2;

            logDebug("做空技术指标确认 - RSI: {}, MACD: {}, 动量: {}, 确认数: {}/3, 结果: {}",
                rsiConfirm ? "通过" : "未通过",
                macdConfirm ? "通过" : "未通过",
                momentumConfirm ? "通过" : "未通过",
                confirmCount,
                result ? "通过" : "未通过");

            return result;
        } catch (Exception e) {
            log.error("做空技术指标确认异常", e);
            return false;
        }
    }

    /**
     * 检查RSI是否适合做空
     */
    private boolean checkRSIForShort(List<KLineEntity> kline) {
        if (kline.size() < config.getRsiPeriod() + 5) {
            return false;
        }

        try {
            // 提取收盘价用于RSI计算
            List<Double> closePrices = extractClosePrices(kline);
            double currentRSI = EMASignalUtil.calculateRSI(closePrices, 0, config.getRsiPeriod());
            double prevRSI = EMASignalUtil.calculateRSI(closePrices, 1, config.getRsiPeriod());

            // RSI超买阈值（通常70以上）
            double rsiOverbought = 100 - config.getRsiOversold(); // 如果超卖是30，那么超买是70
            double rsiFalling = 100 - config.getRsiRising(); // 对应的回落阈值

            // RSI从超买区域回落
            boolean wasOverbought = prevRSI >= rsiOverbought;
            boolean isFalling = currentRSI < rsiFalling;
            boolean isDecreasing = currentRSI < prevRSI;

            return wasOverbought && isFalling && isDecreasing;
        } catch (Exception e) {
            logDebug("RSI做空检查异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查MACD是否适合做空
     */
    private boolean checkMACDForShort(List<KLineEntity> kline) {
        if (kline.size() < 30) {
            return false;
        }

        try {
            // 简化的MACD检查：短期EMA < 长期EMA 且趋势向下
            double ema12_current = calculateEMA(extractClosePrices(kline), 0, 12);
            double ema26_current = calculateEMA(extractClosePrices(kline), 0, 26);
            double ema12_prev = calculateEMA(extractClosePrices(kline), 1, 12);
            double ema26_prev = calculateEMA(extractClosePrices(kline), 1, 26);

            // MACD死叉：短期EMA下穿长期EMA
            boolean currentBearish = ema12_current < ema26_current;
            boolean prevBullish = ema12_prev >= ema26_prev;
            boolean deathCross = currentBearish && prevBullish;

            // 或者已经在空头状态且继续恶化
            boolean continueBearish = currentBearish && (ema12_current - ema26_current) < (ema12_prev - ema26_prev);

            return deathCross || continueBearish;
        } catch (Exception e) {
            logDebug("MACD做空检查异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查价格动量是否适合做空
     */
    private boolean checkMomentumForShort(List<KLineEntity> kline) {
        if (kline.size() < 5) {
            return false;
        }

        try {
            // 检查最近几根K线的价格动量
            double currentClose = kline.get(0).getClosePrice().doubleValue();
            double prevClose = kline.get(1).getClosePrice().doubleValue();
            double prev2Close = kline.get(2).getClosePrice().doubleValue();

            // 连续下跌
            boolean isDecreasing = currentClose < prevClose && prevClose < prev2Close;

            // 下跌幅度足够
            double declinePercent = (prev2Close - currentClose) / prev2Close;
            boolean sufficientDecline = declinePercent > 0.005; // 至少0.5%的下跌

            // 成交量确认
            double currentVolume = kline.get(0).getVolume();
            double avgVolume = kline.stream().limit(5).mapToDouble(KLineEntity::getVolume).average().orElse(0);
            boolean volumeConfirm = currentVolume > avgVolume * 0.8; // 成交量不能太低

            return isDecreasing && sufficientDecline && volumeConfirm;
        } catch (Exception e) {
            logDebug("动量做空检查异常: {}", e.getMessage());
            return false;
        }
    }

    private boolean isPriceInChannel(String instId, double currentPrice, double emaMid, double emaSlow,
                                   boolean isLong, List<KLineEntity> klineList) {
        logDebug("[{}] 开始检查{}通道条件...", instId, isLong ? "做多" : "做空");

        boolean isInChannel = isLong ?
            isPriceInLongChannel(currentPrice, emaMid, emaSlow, klineList) :
            isPriceInShortChannel(currentPrice, emaMid, emaSlow, klineList);

        if (!isInChannel) {
            logPriceNotInChannel(instId, currentPrice, emaMid, emaSlow, isLong, klineList);
            if (config.isEnablePerformanceMonitoring()) {
                performanceMonitor.recordFilterReject(STRATEGY_NAME, StrategyPerformanceMonitor.FilterType.CHANNEL);
            }
        }

        return isInChannel;
    }

    private void logDebug(String format, Object... args) {
        if (config.isEnableDetailedLogging()) {
            log.debug(format, args);
        }
    }

    // 计算EMA的辅助方法（带异常处理）
    private double calculateEMA(List<Double> prices, int offset, int period) {
        return exceptionHandler.safeCalculate(
            "calculateEMA_" + period + "_" + offset,
            () -> {
                exceptionHandler.validateListParameter(prices, "prices", "calculateEMA", period + offset);
                exceptionHandler.validateNumericParameter(period, "period", "calculateEMA");
                return EMASignalUtil.calculateEMA(prices, offset, period);
            },
            0.0
        );
    }

    /**
     * 检查EMA趋势是否连续
     */
    private String checkEmaTrend(List<Double> closePrices) {
        int requiredKlines = config.getEmaSlow() + config.getTrendLookbackPeriod() + 1;
        if (closePrices.size() < requiredKlines) {
            logDebug("K线数据不足以计算EMA趋势，需要至少{}根K线", requiredKlines);
            return "none";
        }

        boolean isUptrend = true;
        boolean isDowntrend = true;
        double totalTrendStrength = 0.0;

        for (int i = 0; i < config.getTrendLookbackPeriod(); i++) {
            double currentEma = calculateEMA(closePrices, i, config.getEmaSlow());
            double previousEma = calculateEMA(closePrices, i + 1, config.getEmaSlow());

            if (currentEma == 0 || previousEma == 0) {
                return "none";
            }

            // 计算趋势强度
            double trendStrength = Math.abs(currentEma - previousEma) / previousEma;
            totalTrendStrength += trendStrength;

            if (currentEma <= previousEma) {
                isUptrend = false;
            }
            if (currentEma >= previousEma) {
                isDowntrend = false;
            }

            if (!isUptrend && !isDowntrend) {
                break;
            }
        }

        // 检查趋势强度
        double avgTrendStrength = totalTrendStrength / config.getTrendLookbackPeriod();
        if (avgTrendStrength < config.getMinTrendStrength()) {
            logDebug("趋势强度不足: {}, 最小要求: {}",
                String.format("%.6f", avgTrendStrength), config.getMinTrendStrength());
            return "none";
        }

        if (isUptrend) return "long";
        if (isDowntrend) return "short";
        return "none";
    }

    /**
     * 检查成交量是否满足条件
     */
    private boolean isVolumeSufficient(List<KLineEntity> klineList) {
        int requiredKlines = config.getVolumeLookbackPeriod() + config.getVolumeTrendPeriod();
        if (klineList.size() < requiredKlines) {
            logDebug("K线数据不足以检查成交量条件，需要至少{}根K线", requiredKlines);
            return false;
        }

        double currentVolume = klineList.get(0).getVolume();

        // 计算平均成交量
        double avgVolume = klineList.stream()
            .skip(1)
            .limit(config.getVolumeLookbackPeriod())
            .mapToDouble(KLineEntity::getVolume)
            .average()
            .orElse(0.0);

        if (avgVolume == 0) {
            logDebug("平均成交量为0，成交量条件检查失败");
            return false;
        }

        // 基础成交量检查
        boolean volumeCheck = currentVolume > avgVolume * config.getVolumeMultiplier();

        // 成交量趋势检查
        boolean volumeTrendCheck = checkVolumeTrend(klineList);

        // 计算成交量强度评分
        double volumeRatio = currentVolume / avgVolume;
        double volumeScore = Math.min(volumeRatio / 2.0, 1.0);

        boolean result = volumeCheck && (volumeTrendCheck || volumeScore > 1.5);

        logDebug("成交量检查 - 当前: {}, 平均: {}, 比率: {:.2f}, 基础: {}, 趋势: {}, 评分: {:.2f}, 结果: {}",
            String.format("%.2f", currentVolume),
            String.format("%.2f", avgVolume),
            volumeRatio,
            volumeCheck ? "通过" : "未通过",
            volumeTrendCheck ? "递增" : "非递增",
            volumeScore,
            result ? "通过" : "未通过");

        return result;
    }

    private boolean checkVolumeTrend(List<KLineEntity> klineList) {
        if (klineList.size() < config.getVolumeTrendPeriod()) {
            return true; // 数据不足时默认通过
        }

        for (int i = 0; i < config.getVolumeTrendPeriod() - 1; i++) {
            double currentVol = klineList.get(i).getVolume();
            double nextVol = klineList.get(i + 1).getVolume();
            if (currentVol <= nextVol) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查市场环境是否适合交易
     */
    private boolean isMarketEnvironmentSuitable(List<KLineEntity> klineList) {
        if (klineList.size() < config.getMarketCheckPeriod()) {
            return true; // 数据不足时默认允许交易
        }

        // 使用EMASignalUtil中的低波动率检查
        boolean isLowVolatility = EMASignalUtil.isLowVolatility(klineList, config.getAtrPeriod());
        if (!isLowVolatility) {
            logDebug("市场波动率过高，不适合交易");
            return false;
        }

        // 计算当前ATR占价格的百分比
        double atr = calculateATR(klineList, 0, config.getAtrPeriod());
        double currentPrice = klineList.get(0).getClosePrice().doubleValue();
        double atrPercent = (atr / currentPrice) * 100;

        boolean volatilityCheck = atrPercent <= config.getMaxMarketVolatility();

        logDebug("市场环境检查 - 低波动: {}, ATR%: {:.2f}, 波动率检查: {}, 结果: {}",
            isLowVolatility ? "是" : "否",
            atrPercent,
            volatilityCheck ? "通过" : "未通过",
            (isLowVolatility && volatilityCheck) ? "适合" : "不适合");

        return isLowVolatility && volatilityCheck;
    }

    /**
     * 计算动态通道宽度（带异常处理）
     */
    private double calculateDynamicChannelWidth(List<KLineEntity> klineList) {
        return exceptionHandler.safeCalculate(
            "calculateDynamicChannelWidth",
            () -> {
                exceptionHandler.validateListParameter(klineList, "klineList", "calculateDynamicChannelWidth", 1);

                double atr = calculateATR(klineList, 0, config.getAtrPeriod());
                KLineEntity currentKline = exceptionHandler.safeGetListElement(klineList, 0, null, "getCurrentKline");

                if (currentKline == null || currentKline.getClosePrice() == null) {
                    throw new IllegalStateException("无法获取当前K线或收盘价");
                }

                double currentPrice = currentKline.getClosePrice().doubleValue();

                if (currentPrice <= 0) {
                    throw new IllegalArgumentException("当前价格必须大于0: " + currentPrice);
                }

                double atrPercent = atr / currentPrice;
                double dynamicWidth = config.getChannelWidthBase() + (atrPercent * config.getAtrChannelMultiplier());

                // 限制通道宽度在合理范围内
                dynamicWidth = Math.max(config.getMinChannelWidth(),
                                       Math.min(config.getMaxChannelWidth(), dynamicWidth));

                logDebug("动态通道宽度计算 - ATR={}, 价格={}, ATR%={:.4f}, 基础宽度={}, 最终宽度={:.4f}",
                    String.format("%.6f", atr),
                    String.format("%.4f", currentPrice),
                    atrPercent * 100,
                    config.getChannelWidthBase(),
                    dynamicWidth);

                return dynamicWidth;
            },
            config.getChannelWidthBase() // 默认值
        );
    }

    /**
     * 检查价格是否在做多通道内
     */
    private boolean isPriceInLongChannel(double price, double ema21, double ema55, List<KLineEntity> klineList) {
        double distance = ema21 - ema55;
        double channelWidth = calculateDynamicChannelWidth(klineList);
        double channelHalfWidth = channelWidth * distance;

        double channelBottom = ema55 - channelHalfWidth;
        double channelTop = ema55 + channelHalfWidth;

        boolean result = price >= channelBottom && price <= channelTop;

        logDebug("做多通道检查 - 价格={}, 下轨={}, 上轨={}, 结果={}",
            String.format("%.4f", price),
            String.format("%.4f", channelBottom),
            String.format("%.4f", channelTop),
            result ? "在通道内" : "不在通道内");

        return result;
    }

    /**
     * 检查价格是否在做空通道内
     */
    private boolean isPriceInShortChannel(double price, double ema21, double ema55, List<KLineEntity> klineList) {
        double distance = ema55 - ema21;
        double channelWidth = calculateDynamicChannelWidth(klineList);
        double channelHalfWidth = channelWidth * distance;

        double channelBottom = ema55 - channelHalfWidth;
        double channelTop = ema55 + channelHalfWidth;

        boolean result = price >= channelBottom && price <= channelTop;

        logDebug("做空通道检查 - 价格={}, 下轨={}, 上轨={}, 结果={}",
            String.format("%.4f", price),
            String.format("%.4f", channelBottom),
            String.format("%.4f", channelTop),
            result ? "在通道内" : "不在通道内");

        return result;
    }

    /**
     * 记录价格未在通道内的日志
     */
    private void logPriceNotInChannel(String instId, double currentPrice, double emaMid, double emaSlow,
                                    boolean isLong, List<KLineEntity> klineList) {
        double distance = isLong ? (emaMid - emaSlow) : (emaSlow - emaMid);
        double channelWidth = calculateDynamicChannelWidth(klineList);
        double channelHalfWidth = channelWidth * distance;

        double channelBottom = emaSlow - channelHalfWidth;
        double channelTop = emaSlow + channelHalfWidth;

        String channelType = isLong ? "做多" : "做空";

        log.info("[{}] 价格 {} 不在{}通道内 (EMA{}={}, EMA{}={}, 动态通道范围={}~{})",
            instId,
            String.format("%.4f", currentPrice),
            channelType,
            config.getEmaMid(), String.format("%.4f", emaMid),
            config.getEmaSlow(), String.format("%.4f", emaSlow),
            String.format("%.4f", channelBottom),
            String.format("%.4f", channelTop));
    }

    /**
     * 检查是否已有交易
     */
    private boolean checkTrade(String key) {
        try {
            // 暂时禁用Redis检查，避免编译错误
             return com.miner.common.utils.redis.RedisUtils.getCacheObject(key) != null;
//            logDebug("检查交易状态 key={}, 结果={} (Redis检查已禁用)", key, hasTrade ? "已有交易" : "无交易");
        } catch (Exception e) {
            log.error("检查交易状态异常", e);
            return false;
        }
    }

    /**
     * 处理入场操作
     */
    private void processEntry(String instId, String side, BigDecimal currentPrice, List<KLineEntity> klineList) {
        try {
            log.info("准备入场: instId={}, side={}, 当前价格={}", instId, side, currentPrice);

            // 计算ATR
            double atr = calculateATR(klineList, 0, config.getAtrPeriod());
            if (atr <= 0) {
                log.info("[{}] ATR计算错误，取消入场", instId);
                return;
            }
            log.info("[{}] 计算ATR({})值为 {}", instId, config.getAtrPeriod(), String.format("%.6f", atr));

            // 计算止盈止损价格
            StopLossAndTakeProfitPrices prices = calculateStopLossAndTakeProfitPrices(currentPrice, atr, side);

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());
            log.info("入场参数: 交易对={}, 方向={}, 数量={}, 止损价={}, 止盈价={}",
                instId, side, sz, prices.stopLossPrice, prices.takeProfitPrice);

            // 执行开仓
            String tradeSide = "long".equals(side) ? "buy" : "sell";
            log.info("[{}] 执行{}开仓操作，数量={}", instId, tradeSide, sz);
            trade(instId, tradeSide, sz, side, false);
            log.info("[{}] 开仓指令已发送，等待仓位更新", instId);
            Thread.sleep(200); // 等待仓位更新

            // 设置止盈止损
            if (config.isEnableBatchTakeProfit()) {
                setupBatchStopOrders(instId, side, sz, prices);
            } else {
                setupStopOrders(instId, side, sz, prices.stopLossPrice, prices.takeProfitPrice);
            }

            // 记录交易信息到Redis
            recordTradeToRedis(instId);

        } catch (Exception e) {
            log.error("执行入场操作异常 for {}", instId, e);
        }
    }

    private static class StopLossAndTakeProfitPrices {
        final BigDecimal stopLossPrice;
        final BigDecimal takeProfitPrice;
        final BigDecimal tpDistance;

        StopLossAndTakeProfitPrices(BigDecimal stopLossPrice, BigDecimal takeProfitPrice, BigDecimal tpDistance) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice = takeProfitPrice;
            this.tpDistance = tpDistance;
        }
    }

    private StopLossAndTakeProfitPrices calculateStopLossAndTakeProfitPrices(BigDecimal currentPrice, double atr, String side) {
        // 计算基于ATR的距离
        BigDecimal atrSlDistance = BigDecimal.valueOf(atr * config.getStopLossAtrMultiplier());
        BigDecimal atrTpDistance = BigDecimal.valueOf(atr * config.getTakeProfitAtrMultiplier());

        // 计算基于百分比的距离
        BigDecimal percentSlDistance = currentPrice.multiply(BigDecimal.valueOf(config.getStopLossPercent()));
        BigDecimal percentTpDistance = currentPrice.multiply(BigDecimal.valueOf(config.getTakeProfitPercent()));

        // 取较大值作为最终距离
        BigDecimal finalSlDistance = atrSlDistance.max(percentSlDistance);
        BigDecimal finalTpDistance = atrTpDistance.max(percentTpDistance);

        log.info("动态风控计算: ATR止损={}, 百分比止损={}, 最终止损={}", atrSlDistance, percentSlDistance, finalSlDistance);
        log.info("动态风控计算: ATR止盈={}, 百分比止盈={}, 最终止盈={}", atrTpDistance, percentTpDistance, finalTpDistance);

        // 计算止盈止损价格
        BigDecimal stopLossPrice;
        BigDecimal takeProfitPrice;

        if ("long".equals(side)) {
            stopLossPrice = currentPrice.subtract(finalSlDistance)
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
            takeProfitPrice = currentPrice.add(finalTpDistance)
                .setScale(currentPrice.scale(), RoundingMode.UP);
        } else {
            stopLossPrice = currentPrice.add(finalSlDistance)
                .setScale(currentPrice.scale(), RoundingMode.UP);
            takeProfitPrice = currentPrice.subtract(finalTpDistance)
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
        }

        return new StopLossAndTakeProfitPrices(stopLossPrice, takeProfitPrice, finalTpDistance);
    }

    private void recordTradeToRedis(String instId) {
        String key = config.getStrategyKeyPrefix() + instId;
        // TODO: 修复Redis依赖问题后重新启用
        // com.miner.common.utils.redis.RedisUtils.setCacheObject(key, "true", Duration.ofHours(config.getRedisCacheHours()));
        log.info("交易信息已记录到内存，key={} (Redis记录已禁用)", key);
    }

    /**
     * 设置分批止盈止损订单
     */
    private void setupBatchStopOrders(String instId, String side, String sz, StopLossAndTakeProfitPrices prices) {
        String orderSide = "long".equals(side) ? "sell" : "buy";

        // 计算分批数量
        double totalSize = Double.parseDouble(sz);
        String firstBatchSize = String.valueOf(Math.round(totalSize * config.getFirstBatchRatio()));
        String secondBatchSize = String.valueOf(totalSize - Double.parseDouble(firstBatchSize));

        // 计算第二个止盈价格
        BigDecimal currentPrice = "long".equals(side) ?
            prices.takeProfitPrice.subtract(prices.tpDistance) : prices.takeProfitPrice.add(prices.tpDistance);
        BigDecimal secondTakeProfitPrice;

        if ("long".equals(side)) {
            secondTakeProfitPrice = currentPrice.add(prices.tpDistance.multiply(BigDecimal.valueOf(config.getSecondBatchMultiplier())))
                .setScale(currentPrice.scale(), RoundingMode.UP);
        } else {
            secondTakeProfitPrice = currentPrice.subtract(prices.tpDistance.multiply(BigDecimal.valueOf(config.getSecondBatchMultiplier())))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
        }

        // 设置止损单（全部数量）
        log.info("[{}] 设置止损单 - 方向: {}, 价格: {}, 数量: {}", instId, orderSide, prices.stopLossPrice, sz);
        algoTradeLoss(instId, orderSide, sz, side, prices.stopLossPrice.toString());

        // 设置第一批止盈单
        log.info("[{}] 设置第一批止盈单 - 方向: {}, 价格: {}, 数量: {}", instId, orderSide, prices.takeProfitPrice, firstBatchSize);
        algoTradeWin(instId, orderSide, firstBatchSize, side, prices.takeProfitPrice.toString());

        // 设置第二批止盈单
        log.info("[{}] 设置第二批止盈单 - 方向: {}, 价格: {}, 数量: {}", instId, orderSide, secondTakeProfitPrice, secondBatchSize);
        algoTradeWin(instId, orderSide, secondBatchSize, side, secondTakeProfitPrice.toString());

        // 发送消息通知
        sendBatchTradingNotification(instId, side, prices, firstBatchSize, secondTakeProfitPrice, secondBatchSize);
    }

    /**
     * 设置普通止盈止损订单
     */
    private void setupStopOrders(String instId, String side, String sz, BigDecimal stopLossPrice, BigDecimal takeProfitPrice) {
        String orderSide = "long".equals(side) ? "sell" : "buy";

        // 设置止损单
        log.info("[{}] 设置止损单 - 方向: {}, 价格: {}, 数量: {}", instId, orderSide, stopLossPrice, sz);
        algoTradeLoss(instId, orderSide, sz, side, stopLossPrice.toString());

        // 设置止盈单
        log.info("[{}] 设置止盈单 - 方向: {}, 价格: {}, 数量: {}", instId, orderSide, takeProfitPrice, sz);
        algoTradeWin(instId, orderSide, sz, side, takeProfitPrice.toString());

        // 发送消息通知
        sendTradingNotification(instId, side, stopLossPrice, takeProfitPrice);
    }

    private void sendBatchTradingNotification(String instId, String side, StopLossAndTakeProfitPrices prices,
                                            String firstBatchSize, BigDecimal secondTakeProfitPrice, String secondBatchSize) {
        String avgPx = getAveragePrice(instId);
        String message = MessageFormat.format("{0} {1}分批入场: 入场价约={2}, 止损价={3}, 止盈1={4}({5}), 止盈2={6}({7})",
            instId, side, avgPx, prices.stopLossPrice, prices.takeProfitPrice, firstBatchSize, secondTakeProfitPrice, secondBatchSize);
        log.info("[{}] 发送消息通知: {}", instId, message);
        messageService.send(instId + " " + side + "单策略", message);
    }

    private void sendTradingNotification(String instId, String side, BigDecimal stopLossPrice, BigDecimal takeProfitPrice) {
        String avgPx = getAveragePrice(instId);
        String message = MessageFormat.format("{0} {1}入场: 入场价约={2}, 止损价={3}, 止盈价={4}",
            instId, side, avgPx, stopLossPrice, takeProfitPrice);
        log.info("[{}] 发送消息通知: {}", instId, message);
        messageService.send(instId + " " + side + "单策略", message);
    }

    private String getAveragePrice(String instId) {
        try {
            return getPos(instId).getAvgPx();
        } catch (Exception e) {
            log.warn("[{}] 获取入场均价失败", instId, e);
            return "未知";
        }
    }
}
