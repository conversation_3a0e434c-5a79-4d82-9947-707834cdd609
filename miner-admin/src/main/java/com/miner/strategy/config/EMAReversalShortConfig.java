package com.miner.strategy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * EMA反转做空策略配置
 *
 * 策略逻辑：
 * 1. 前100根15分钟K线中存在连续40根收盘价小于EMA55
 * 2. 在4小时内价格反弹到EMA55上方
 * 3. 最近5根K线ADX存在大于55的情况
 * 4. 前两根K线ADX连续下降
 * 5. 做空入场，止损4倍ATR，止盈16倍ATR
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "strategy.ema-reversal-short")
public class EMAReversalShortConfig {

    // ========== 基础配置 ==========
    /**
     * 是否启用策略
     */
    private boolean enabled = true;

    /**
     * 交易对数量限制
     */
    private int instIdLimit = 20;

    /**
     * 处理间隔时间（毫秒）
     */
    private long processingInterval = 1000;

    /**
     * 是否启用详细日志
     */
    private boolean enableDetailedLogging = false;

    // ========== 历史趋势确认参数 ==========
    /**
     * 历史K线检查周期
     */
    private int historyPeriod = 100;

    /**
     * 连续下跌K线数量要求
     */
    private int consecutiveDownBars = 40;

    /**
     * EMA周期
     */
    private int emaPeriod = 55;

    // ========== 反弹监控参数 ==========
    /**
     * 反弹监控时间窗口（4小时 = 16个15分钟K线）
     */
    private int reboundTimeWindow = 16;

    /**
     * 反弹确认的最小涨幅百分比
     */
    private double minReboundPercent = 0.005; // 0.5%

    // ========== ADX确认参数 ==========
    /**
     * ADX计算周期
     */
    private int adxPeriod = 14;

    /**
     * ADX强度阈值
     */
    private double adxThreshold = 55.0;

    /**
     * ADX检查的K线数量
     */
    private int adxCheckPeriod = 5;

    // ========== 风险管理参数 ==========
    /**
     * ATR计算周期
     */
    private int atrPeriod = 14;

    /**
     * 止损倍数
     */
    private double stopLossMultiplier = 4.0;

    /**
     * 止盈倍数
     */
    private double takeProfitMultiplier = 16.0;

    // ========== 仓位管理参数 ==========
    /**
     * 仓位大小百分比（基于账户资金）
     */
    private double positionSizePercent = 0.02; // 2%

    /**
     * 每日最大交易次数
     */
    private int maxDailyTrades = 3;

    // ========== 过滤器参数 ==========
    /**
     * 是否启用成交量过滤
     */
    private boolean enableVolumeFilter = true;

    /**
     * 成交量倍数要求
     */
    private double volumeMultiplier = 0.8;

    /**
     * 是否启用波动率过滤
     */
    private boolean enableVolatilityFilter = true;

    /**
     * 最大波动率倍数
     */
    private double maxVolatilityMultiplier = 2.0;

    // ========== 时间过滤参数 ==========
    /**
     * 是否启用时间过滤
     */
    private boolean enableTimeFilter = false;

    /**
     * 禁止交易的小时（24小时制）
     */
    private int[] forbiddenHours = {0, 1, 2, 3, 4, 5}; // 凌晨0-5点

    // ========== 风险控制参数 ==========
    /**
     * 最大回撤百分比
     */
    private double maxDrawdownPercent = 0.10; // 10%

    /**
     * 连续亏损次数限制
     */
    private int maxConsecutiveLosses = 3;

    /**
     * 连续亏损后暂停时间（分钟）
     */
    private int pauseAfterLossesMinutes = 60;

    // ========== 监控和通知参数 ==========
    /**
     * 是否启用性能监控
     */
    private boolean enablePerformanceMonitoring = true;

    /**
     * 是否启用推送通知
     */
    private boolean enablePushNotification = true;

    /**
     * 推送通知的最小盈亏金额
     */
    private double minNotificationAmount = 10.0;

    // ========== Redis管理参数 ==========
    /**
     * 下跌趋势候选列表的过期时间（天）
     */
    private int downtrendCandidatesExpirationDays = 7;

    /**
     * 监控候选列表的过期时间（小时）
     */
    private int monitoringCandidatesExpirationHours = 24;

    /**
     * 是否启用Redis候选列表管理
     */
    private boolean enableRedisCandidateManagement = true;

    /**
     * 是否在启动时清理过期的Redis数据
     */
    private boolean cleanupExpiredDataOnStartup = true;

    // ========== 调试参数 ==========
    /**
     * 是否启用模拟模式（不实际下单）
     */
    private boolean simulationMode = false;

    /**
     * 是否保存交易信号到Redis
     */
    private boolean saveSignalsToRedis = true;

    /**
     * 信号保存的过期时间（小时）
     */
    private int signalExpirationHours = 24;
}
