package com.miner.strategy;

import cn.hutool.core.thread.ThreadUtil;
import com.miner.common.utils.redis.RedisUtils;
import com.miner.strategy.config.EMAReversalShortConfig;
import com.miner.strategy.exception.StrategyExceptionHandler;
import com.miner.strategy.monitor.StrategyPerformanceMonitor;
import com.miner.strategy.util.EMASignalUtil;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.miner.strategy.util.EMASignalUtil.calculateATR;

/**
 * EMA反转做空策略
 * <p>
 * 策略逻辑：
 * 1. 前100根15分钟K线中存在连续40根收盘价小于EMA55
 * 2. 在4小时内价格反弹到EMA55上方
 * 3. 最近5根K线ADX存在大于55的情况
 * 4. 前两根K线ADX连续下降
 * 5. 做空入场，止损4倍ATR，止盈16倍ATR
 *
 * <AUTHOR>
 */
@Component("emaReversalShortStrategy")
@Slf4j
public class EMAReversalShortStrategy extends BaseStrategy {

    @Resource
    private EMAReversalShortConfig config;

    @Resource
    private StrategyPerformanceMonitor performanceMonitor;

    @Resource
    private StrategyExceptionHandler exceptionHandler;

    private static final String STRATEGY_NAME = "EMAReversalShortStrategy";

    // Redis键名常量
    private static final String DOWNTREND_CANDIDATES_KEY = "ema_reversal_short:downtrend_candidates";
    private static final String MONITORING_CANDIDATES_KEY = "ema_reversal_short:monitoring_candidates";
    private static final String PROCESSED_KEY = "ema_reversal_short:";

    // 策略状态枚举
    private enum StrategyState {
        WAITING,      // 等待历史下跌趋势条件
        MONITORING,   // 监控反弹阶段
        READY,        // 准备入场
        POSITIONED    // 已持仓
    }

    @Override
    public void run() {
        if (!config.isEnabled()) {
            log.info("EMA反转做空策略已禁用，跳过执行");
            return;
        }

        log.info("==== EMA反转做空策略开始执行 ====");

        // 启动性能监控
        if (config.isEnablePerformanceMonitoring()) {
            performanceMonitor.startMonitoring(STRATEGY_NAME);
        }

        // 扫描交易对，寻找下跌趋势
        scan();

    }

    /**
     * 处理Redis中的候选交易对
     */
    private void processCandidatesFromRedis() {
        log.info("开始处理Redis中的候选交易对...");

        try {
            // 获取下跌趋势候选列表
            List<String> downtrendCandidates = RedisUtils.getCacheList(DOWNTREND_CANDIDATES_KEY);
            if (downtrendCandidates != null && !downtrendCandidates.isEmpty()) {
                log.info("发现{}个下跌趋势候选交易对", downtrendCandidates.size());

                for (String instId : downtrendCandidates) {
                    try {
                        logProgress(instId);
                        boolean processed = processDowntrendCandidate(instId);

                        // 如果已经入场或者不再满足条件，从候选列表中移除
                        if (processed) {
                            removeFromDowntrendCandidates(instId);
                        }

                        ThreadUtil.sleep(config.getProcessingInterval());
                    } catch (Exception e) {
                        log.error("处理下跌趋势候选交易对 {} 异常", instId, e);
                    }
                }
            }

            // 获取监控中的候选列表
            List<String> monitoringCandidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            if (monitoringCandidates != null && !monitoringCandidates.isEmpty()) {
                log.info("发现{}个监控中的候选交易对", monitoringCandidates.size());

                for (String instId : monitoringCandidates) {
                    try {
                        logProgress(instId);
                        boolean processed = process(instId);

                        // 如果已经入场或者超时，从监控列表中移除
                        if (processed) {
                        }

                        ThreadUtil.sleep(config.getProcessingInterval());
                    } catch (Exception e) {
                        log.error("处理监控候选交易对 {} 异常", instId, e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理Redis候选交易对异常", e);
        }
    }

    /**
     * 扫描新的下跌趋势候选交易对
     */
    private void scan() {
        log.info("开始扫描新的下跌趋势候选交易对...");

        try {

            // 获取交易对列表
            List<String> instIds = getInstIds(config.getInstIdLimit());
            if (instIds.isEmpty()) {
                log.warn("未获取到交易对列表");
                return;
            }

            log.info("获取到{}个交易对，开始扫描下跌趋势", instIds.size());

            int newCandidatesCount = 0;
            for (String instId : instIds) {
                try {
                    boolean candidate = RedisUtils.getCacheObject(PROCESSED_KEY + instId) != null;
                    if (candidate && getPos(instId) == null) {
                        if (processDowntrendCandidate(instId)) {
                            process(instId);
                        }
                        continue;
                    }

                    // 跳过已有仓位的交易对
                    if (hasExistingTrade(instId)) {
                        continue;
                    }

                    logProgress(instId);
                    boolean isNewCandidate = scanForDowntrend(instId);

                    if (isNewCandidate) {
                        newCandidatesCount++;
                    }


                    ThreadUtil.sleep(config.getProcessingInterval());
                } catch (Exception e) {
                    log.error("扫描交易对 {} 下跌趋势异常", instId, e);
                }
            }

            log.info("扫描完成，发现{}个新的下跌趋势候选交易对", newCandidatesCount);

        } catch (Exception e) {
            log.error("扫描新下跌趋势候选交易对异常", e);
        }
    }

    /**
     * 处理下跌趋势候选交易对
     */
    private boolean processDowntrendCandidate(String instId) {
        logDebug("[{}] 处理下跌趋势候选交易对...", instId);

        // 检查是否已有仓位
        if (hasExistingTrade(instId)) {
            logDebug("[{}] 已有仓位，从候选列表移除", instId);
            return true;
        }

        // 检查风险控制
        if (!checkRiskLimits(instId)) {
            return false;
        }

        // 获取K线数据
        List<KLineEntity> klineData = getKlineData(instId);
        if (klineData == null || klineData.size() < 50) {
            logDebug("[{}] K线数据不足", instId);
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = extractClosePrices(klineData);

        // 检查是否仍然满足下跌趋势（可能市场已经变化）
        if (!checkHistoricalDowntrend(instId, klineData, closePrices)) {
            logDebug("[{}] 不再满足下跌趋势条件，从候选列表移除", instId);
            return true;
        }

        // 检查反弹条件
        if (checkRebound(instId, klineData, closePrices)) {
            // 反弹条件满足，移动到监控列表
            logDebug("[{}] 检测到反弹，移动到监控列表", instId);
            return true;
        }

        return false;
    }

    /**
     * 处理监控中的候选交易对
     */
    private boolean process(String instId) {
        logDebug("[{}] 处理监控中的候选交易对...", instId);

        // 检查是否已有仓位
        if (hasExistingTrade(instId)) {
            logDebug("[{}] 已有仓位，从监控列表移除", instId);
            return true;
        }

        // 检查风险控制
        if (!checkRiskLimits(instId)) {
            return false;
        }

        // 获取K线数据
        List<KLineEntity> klineData = getKlineData(instId);
        if (klineData == null || klineData.size() < 50) {
            logDebug("[{}] K线数据不足", instId);
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = extractClosePrices(klineData);


        // 检查反弹条件是否仍然满足
        if (!checkRebound(instId, klineData, closePrices)) {
            logDebug("[{}] 反弹条件不再满足，从监控列表移除", instId);
            return true;
        }

        // 检查ADX条件
        if (!checkADXConditions(instId, klineData)) {
            return false;
        }

        // 执行其他过滤器
//        if (!passAllFilters(instId, klineData)) {
//            return false;
//        }

        // 满足所有入场条件，执行入场
        handleEntry(instId, klineData);
        return true;
    }

    /**
     * 扫描单个交易对的下跌趋势
     */
    private boolean scanForDowntrend(String instId) {
        logDebug("[{}] 扫描下跌趋势...", instId);

        // 获取K线数据
        List<KLineEntity> klineData = getKlineData(instId);
        if (klineData == null || klineData.size() < config.getHistoryPeriod() + 20) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = extractClosePrices(klineData);

        // 检查历史下跌趋势
        if (checkHistoricalDowntrend(instId, klineData, closePrices)) {
            // 添加到下跌趋势候选列表
            addToCandidates(instId);
            log.info("[{}] 发现下跌趋势，添加到候选列表", instId);
            return true;
        }

        return false;
    }

    /**
     * 处理单个交易对（保留原方法作为备用）
     */
    private boolean processInstrument(String instId) {
        // 检查是否已有仓位
        if (hasExistingTrade(instId)) {
            return false;
        }

        // 检查风险控制
        if (!checkRiskLimits(instId)) {
            return false;
        }

        // 获取K线数据（需要足够的历史数据）
        List<KLineEntity> klineData = getKlineData(instId);
        if (klineData == null || klineData.size() < config.getHistoryPeriod() + 20) {
            logDebug("[{}] K线数据不足，需要至少{}根，实际{}根",
                instId, config.getHistoryPeriod() + 20, klineData != null ? klineData.size() : 0);
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = extractClosePrices(klineData);

        // 步骤1：检查历史下跌趋势
        if (!checkHistoricalDowntrend(instId, klineData, closePrices)) {
            return false;
        }

        // 步骤2：检查反弹条件
        if (!checkRebound(instId, klineData, closePrices)) {
            return false;
        }

        // 步骤3：检查ADX条件
        if (!checkADXConditions(instId, klineData)) {
            return false;
        }

        // 步骤4：执行其他过滤器
        if (!passAllFilters(instId, klineData)) {
            return false;
        }

        // 步骤5：执行入场
        handleEntry(instId, klineData);
        return true;
    }

    /**
     * 检查历史下跌趋势
     * 前100根K线中存在连续40根收盘价小于EMA55
     */
    private boolean checkHistoricalDowntrend(String instId, List<KLineEntity> klineData, List<Double> closePrices) {
        logDebug("[{}] 开始检查历史下跌趋势...", instId);

        int consecutiveCount = 0;
        int maxConsecutive = 0;

        // 检查前100根K线
        int checkPeriod = Math.min(config.getHistoryPeriod(), klineData.size() - 20);

        // 从第20根开始，保留最新20根用于其他判断
        for (int i = 20; i < 20 + checkPeriod; i++) {
            double closePrice = klineData.get(i).getClosePrice().doubleValue();
            double ema55 = calculateEMA(closePrices, i, config.getEmaPeriod());

            if (closePrice < ema55) {
                consecutiveCount++;
                maxConsecutive = Math.max(maxConsecutive, consecutiveCount);
            } else {
                consecutiveCount = 0;
            }
        }

        boolean hasDowntrend = maxConsecutive >= config.getConsecutiveDownBars();

        logDebug("[{}] 历史下跌趋势检查 - 最大连续下跌: {}, 要求: {}, 结果: {}",
            instId, maxConsecutive, config.getConsecutiveDownBars(), hasDowntrend ? "通过" : "未通过");

        return hasDowntrend;
    }

    /**
     * 检查反弹条件
     * 在4小时内价格反弹到EMA55上方
     */
    private boolean checkRebound(String instId, List<KLineEntity> klineData, List<Double> closePrices) {
        logDebug("[{}] 开始检查反弹条件...", instId);

        // 当前价格和EMA55
        double currentPrice = klineData.get(0).getClosePrice().doubleValue();
        double currentEMA55 = calculateEMA(closePrices, 0, config.getEmaPeriod());

        // 检查当前价格是否在EMA55上方
        if (currentPrice <= currentEMA55) {
            logDebug("[{}] 当前价格{}未站上EMA55{}", instId,
                String.format("%.4f", currentPrice), String.format("%.4f", currentEMA55));
            return false;
        }

        // 检查在反弹时间窗口内是否有价格低于EMA55的情况
        boolean hasRebound = false;
        int windowSize = Math.min(config.getReboundTimeWindow(), klineData.size());

        for (int i = 1; i < windowSize; i++) {
            double pastPrice = klineData.get(i).getClosePrice().doubleValue();
            double pastEMA55 = calculateEMA(closePrices, i, config.getEmaPeriod());

            if (pastPrice < pastEMA55) {
                // 找到了价格低于EMA55的情况，确认这是一个反弹
                double reboundPercent = (currentPrice - pastPrice) / pastPrice;
                if (reboundPercent >= config.getMinReboundPercent()) {
                    hasRebound = true;
                    logDebug("[{}] 发现有效反弹 - 从{}反弹至{}，涨幅{:.2f}%",
                        instId, String.format("%.4f", pastPrice),
                        String.format("%.4f", currentPrice), reboundPercent * 100);
                    break;
                }
            }
        }

        logDebug("[{}] 反弹条件检查 - 当前价格: {}, EMA55: {}, 结果: {}",
            instId, String.format("%.4f", currentPrice), String.format("%.4f", currentEMA55),
            hasRebound ? "通过" : "未通过");

        return hasRebound;
    }

    /**
     * 检查ADX条件
     * 1. 最近5根K线ADX存在大于55的情况
     * 2. 前两根K线ADX连续下降
     */
    private boolean checkADXConditions(String instId, List<KLineEntity> klineData) {
        logDebug("[{}] 开始检查ADX条件...", instId);

        try {
            // 检查最近5根K线ADX是否有大于阈值的
            boolean hasHighADX = false;
            double maxADX = 0;

            for (int i = 0; i < config.getAdxCheckPeriod(); i++) {
                double adx = calculateADX(klineData, i, config.getAdxPeriod());
                maxADX = Math.max(maxADX, adx);
                if (adx > config.getAdxThreshold()) {
                    hasHighADX = true;
                    logDebug("[{}] 第{}根K线ADX为{:.2f}，超过阈值{}",
                        instId, i, adx, config.getAdxThreshold());
                }
            }

            if (!hasHighADX) {
                logDebug("[{}] 最近{}根K线ADX最大值{:.2f}，未超过阈值{}",
                    instId, config.getAdxCheckPeriod(), maxADX, config.getAdxThreshold());
                return false;
            }

            // 检查前两根K线ADX连续下降
            double adx1 = calculateADX(klineData, 1, config.getAdxPeriod());
            double adx2 = calculateADX(klineData, 2, config.getAdxPeriod());

            boolean isDecreasing = adx1 < adx2;

            logDebug("[{}] ADX下降检查 - ADX[1]: {:.2f}, ADX[2]: {:.2f}, 下降: {}",
                instId, adx1, adx2, isDecreasing ? "是" : "否");

            return isDecreasing;

        } catch (Exception e) {
            log.error("[{}] ADX条件检查异常: {}", instId, e.getMessage());
            return false;
        }
    }

    /**
     * 获取K线数据
     */
    private List<KLineEntity> getKlineData(String instId) {
        try {
            // 获取足够的15分钟K线数据
            int requiredSize = config.getHistoryPeriod() + 50; // 多获取一些数据确保计算准确
            return getKline(instId, "15m", String.valueOf(requiredSize), false);
        } catch (Exception e) {
            log.error("[{}] 获取K线数据异常: {}", instId, e.getMessage());
            return null;
        }
    }

    /**
     * 提取收盘价列表
     */
    private List<Double> extractClosePrices(List<KLineEntity> klineData) {
        return klineData.stream()
            .map(k -> k.getClosePrice().doubleValue())
            .collect(Collectors.toList());
    }

    /**
     * 计算EMA
     */
    private double calculateEMA(List<Double> prices, int offset, int period) {
        return exceptionHandler.safeCalculate(
            "calculateEMA_" + period + "_" + offset,
            () -> {
                exceptionHandler.validateListParameter(prices, "prices", "calculateEMA", period + offset);
                exceptionHandler.validateNumericParameter(period, "period", "calculateEMA");
                return EMASignalUtil.calculateEMA(prices, offset, period);
            },
            0.0
        );
    }

    /**
     * 计算ADX指标
     */
    private double calculateADX(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < period + offset + 1) {
            return 0.0;
        }

        // 计算+DI和-DI
        double[] diValues = calculateDI(klineList, offset, period);
        double plusDI = diValues[0];
        double minusDI = diValues[1];

        // 计算方向指数DX
        double dx = 0.0;
        if (plusDI + minusDI > 0) {
            dx = 100 * Math.abs(plusDI - minusDI) / (plusDI + minusDI);
        }

        // 计算ADX（DX的平滑平均）
        double adx = dx;
        for (int i = 1; i < period; i++) {
            if (klineList.size() <= offset + i + period) {
                break;
            }
            double[] prevDI = calculateDI(klineList, offset + i, period);
            double prevPlusDI = prevDI[0];
            double prevMinusDI = prevDI[1];

            double prevDX = 0.0;
            if (prevPlusDI + prevMinusDI > 0) {
                prevDX = 100 * Math.abs(prevPlusDI - prevMinusDI) / (prevPlusDI + prevMinusDI);
            }

            adx = ((period - 1) * adx + prevDX) / period;
        }

        return adx;
    }

    /**
     * 计算方向指标DI
     *
     * @return double数组，[0]=+DI，[1]=-DI
     */
    private double[] calculateDI(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < period + offset + 1) {
            return new double[]{0.0, 0.0};
        }

        double sumTR = 0.0;
        double sumPlusDM = 0.0;
        double sumMinusDM = 0.0;

        for (int i = offset; i < offset + period; i++) {
            KLineEntity current = klineList.get(i);
            KLineEntity previous = klineList.get(i + 1);

            double high = current.getHigh().doubleValue();
            double low = current.getLow().doubleValue();
            double prevHigh = previous.getHigh().doubleValue();
            double prevLow = previous.getLow().doubleValue();
            double prevClose = previous.getClose().doubleValue();

            // 计算TR (True Range)
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(Math.max(tr1, tr2), tr3);
            sumTR += tr;

            // 计算DM (Directional Movement)
            double upMove = high - prevHigh;
            double downMove = prevLow - low;

            if (upMove > downMove && upMove > 0) {
                sumPlusDM += upMove;
            }
            if (downMove > upMove && downMove > 0) {
                sumMinusDM += downMove;
            }
        }

        if (sumTR == 0) {
            return new double[]{0.0, 0.0};
        }

        double plusDI = (sumPlusDM / sumTR) * 100;
        double minusDI = (sumMinusDM / sumTR) * 100;

        return new double[]{plusDI, minusDI};
    }

    /**
     * 执行所有过滤器检查
     */
    private boolean passAllFilters(String instId, List<KLineEntity> klineData) {
        // 成交量过滤
        if (config.isEnableVolumeFilter() && !checkVolumeFilter(instId, klineData)) {
            return false;
        }

        // 波动率过滤
        if (config.isEnableVolatilityFilter() && !checkVolatilityFilter(instId, klineData)) {
            return false;
        }

        // 时间过滤
        if (config.isEnableTimeFilter() && !checkTimeFilter(instId)) {
            return false;
        }

        return true;
    }

    /**
     * 检查成交量过滤
     */
    private boolean checkVolumeFilter(String instId, List<KLineEntity> klineData) {
        try {
            double currentVolume = klineData.get(0).getVolume();
            double avgVolume = klineData.stream()
                .limit(5)
                .mapToDouble(KLineEntity::getVolume)
                .average()
                .orElse(0);

            boolean result = currentVolume > avgVolume * config.getVolumeMultiplier();

            logDebug("[{}] 成交量过滤 - 当前: {}, 平均: {}, 倍数要求: {}, 结果: {}",
                instId, String.format("%.2f", currentVolume), String.format("%.2f", avgVolume),
                config.getVolumeMultiplier(), result ? "通过" : "未通过");

            return result;
        } catch (Exception e) {
            log.error("[{}] 成交量过滤检查异常: {}", instId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查波动率过滤
     */
    private boolean checkVolatilityFilter(String instId, List<KLineEntity> klineData) {
        try {
            double currentATR = calculateATR(klineData, 0, config.getAtrPeriod());
            double avgATR = 0;

            // 计算历史平均ATR
            for (int i = 1; i <= 20; i++) {
                avgATR += calculateATR(klineData, i, config.getAtrPeriod());
            }
            avgATR /= 20;

            boolean result = currentATR <= avgATR * config.getMaxVolatilityMultiplier();

            logDebug("[{}] 波动率过滤 - 当前ATR: {}, 平均ATR: {}, 最大倍数: {}, 结果: {}",
                instId, String.format("%.6f", currentATR), String.format("%.6f", avgATR),
                config.getMaxVolatilityMultiplier(), result ? "通过" : "未通过");

            return result;
        } catch (Exception e) {
            log.error("[{}] 波动率过滤检查异常: {}", instId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查时间过滤
     */
    private boolean checkTimeFilter(String instId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int currentHour = now.getHour();

            for (int forbiddenHour : config.getForbiddenHours()) {
                if (currentHour == forbiddenHour) {
                    logDebug("[{}] 时间过滤 - 当前时间{}点在禁止交易时段内", instId, currentHour);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("[{}] 时间过滤检查异常: {}", instId, e.getMessage());
            return true; // 异常时允许交易
        }
    }

    /**
     * 检查风险限制
     */
    private boolean checkRiskLimits(String instId) {
        // 这里可以添加各种风险控制逻辑
        // 例如：检查连续亏损次数、日内交易次数等
        return false;
    }

    /**
     * 检查是否已有仓位
     */
    private boolean hasExistingTrade(String instId) {
        return false;
    }

    /**
     * 处理入场逻辑
     */
    private void handleEntry(String instId, List<KLineEntity> klineData) {
        try {
            BigDecimal currentPrice = klineData.get(0).getClosePrice();
            log.info("[{}] 满足所有入场条件，准备做空入场，当前价格: {}", instId, currentPrice);

            if (config.isSimulationMode()) {
                log.info("[{}] 模拟模式 - 做空信号生成，价格: {}", instId, currentPrice);
                return;
            }

            // 计算ATR
            double atr = calculateATR(klineData, 0, config.getAtrPeriod());
            if (atr <= 0) {
                log.warn("[{}] ATR计算错误，取消入场", instId);
                return;
            }

            // 计算止盈止损价格
            StopLossAndTakeProfitPrices prices = calculateStopLossAndTakeProfitPrices(currentPrice, atr);

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());

            log.info("[{}] 入场参数 - 方向: 做空, 数量: {}, 止损: {}, 止盈: {}, ATR: {}",
                instId, sz, prices.stopLossPrice, prices.takeProfitPrice, String.format("%.6f", atr));

            // 执行开仓
            log.info("[{}] 执行做空开仓操作，数量: {}", instId, sz);
            trade(instId, "sell", sz, "short", false);
            log.info("[{}] 开仓指令已发送，等待仓位更新", instId);
            Thread.sleep(200); // 等待仓位更新

            // 设置止盈止损
            setupStopOrders(instId, sz, prices);

            // 发送通知
            if (config.isEnablePushNotification()) {
                sendTradingNotification(instId, currentPrice, prices, sz);
            }

        } catch (Exception e) {
            log.error("[{}] 执行入场操作异常", instId, e);
        }
    }

    /**
     * 计算止盈止损价格
     */
    private StopLossAndTakeProfitPrices calculateStopLossAndTakeProfitPrices(BigDecimal currentPrice, double atr) {
        double price = currentPrice.doubleValue();

        // 做空：止损在上方，止盈在下方
        double stopLossPrice = price + (atr * config.getStopLossMultiplier());
        double takeProfitPrice = price - (atr * config.getTakeProfitMultiplier());

        // 保持价格精度
        int scale = currentPrice.scale();

        return new StopLossAndTakeProfitPrices(
            new BigDecimal(stopLossPrice).setScale(scale, RoundingMode.HALF_UP),
            new BigDecimal(takeProfitPrice).setScale(scale, RoundingMode.HALF_UP)
        );
    }

    /**
     * 设置止盈止损订单
     */
    private void setupStopOrders(String instId, String sz, StopLossAndTakeProfitPrices prices) {
        try {
            // 做空的平仓方向是买入
            String orderSide = "buy";
            String posSide = "short";

            // 设置止损单
            log.info("[{}] 设置止损单 - 方向: {}, 价格: {}, 数量: {}",
                instId, orderSide, prices.stopLossPrice, sz);
            algoTradeLoss(instId, orderSide, sz, posSide, prices.stopLossPrice.toString());

            // 设置止盈单
            log.info("[{}] 设置止盈单 - 方向: {}, 价格: {}, 数量: {}",
                instId, orderSide, prices.takeProfitPrice, sz);
            algoTradeWin(instId, orderSide, sz, posSide, prices.takeProfitPrice.toString());

        } catch (Exception e) {
            log.error("[{}] 设置止盈止损订单异常", instId, e);
        }
    }


    /**
     * 发送交易通知
     */
    private void sendTradingNotification(String instId, BigDecimal entryPrice,
                                         StopLossAndTakeProfitPrices prices, String size) {
        try {
            String message = String.format(
                "🔥 EMA反转做空策略信号\n" +
                    "交易对: %s\n" +
                    "方向: %s\n" +
                    "入场价: %s\n" +
                    "止损价: %s\n" +
                    "止盈价: %s\n" +
                    "数量: %s\n" +
                    "时间: %s",
                instId, "做空", entryPrice, prices.stopLossPrice, prices.takeProfitPrice, size,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            // 这里需要调用推送服务
            messageService.send("EMA反转做空策略", message);
            log.info("[{}] 交易通知已发送", instId);
        } catch (Exception e) {
            log.error("[{}] 发送交易通知异常: {}", instId, e.getMessage());
        }
    }

    /**
     * 记录处理进度
     */
    private void logProgress(String instId) {
        if (config.isEnableDetailedLogging()) {
            log.debug("开始处理交易对: {}", instId);
        }
    }

    /**
     * 记录最终统计
     */
    private void logFinalStats() {
        if (config.isEnablePerformanceMonitoring()) {
            log.info("==== EMA反转做空策略执行完成 ====");

            // 清理过期数据
            cleanupExpiredData();

            // 输出Redis统计信息
            logRedisStats();
        }
    }

    /**
     * 调试日志
     */
    private void logDebug(String format, Object... args) {
        if (config.isEnableDetailedLogging()) {
            log.debug(format, args);
        }
    }

    // ========== Redis操作方法 ==========

    /**
     * 从下跌趋势候选列表移除
     */
    private void removeFromDowntrendCandidates(String instId) {
        try {
            List<String> candidates = RedisUtils.getCacheList(DOWNTREND_CANDIDATES_KEY);
            if (candidates != null && candidates.contains(instId)) {
                candidates.remove(instId);
                RedisUtils.setCacheList(DOWNTREND_CANDIDATES_KEY, candidates);
                logDebug("[{}] 已从下跌趋势候选列表移除", instId);
            }
        } catch (Exception e) {
            log.error("[{}] 从下跌趋势候选列表移除异常: {}", instId, e.getMessage());
        }
    }

    /**
     * 添加到监控候选列表
     */
    private void addToCandidates(String instId) {
        try {
            String key = PROCESSED_KEY + instId;
            RedisUtils.setCacheObject(key, List.of(instId));
            // 设置过期时间为当天结束
            RedisUtils.expire(key, Duration.ofHours(4));
        } catch (Exception ignored) {
        }
    }


    /**
     * 检查监控是否超时
     */
    private boolean isMonitoringTimeout(String instId) {
        try {
            List<String> candidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            if (candidates != null) {
                for (String candidate : candidates) {
                    if (candidate.startsWith(instId + ":")) {
                        String[] parts = candidate.split(":");
                        if (parts.length >= 2) {
                            long addTime = Long.parseLong(parts[1]);
                            long currentTime = System.currentTimeMillis();
                            long timeWindow = config.getReboundTimeWindow() * 15 * 60 * 1000L; // 转换为毫秒

                            boolean timeout = (currentTime - addTime) > timeWindow;
                            if (timeout) {
                                logDebug("[{}] 监控超时，添加时间: {}, 当前时间: {}, 窗口: {}分钟",
                                    instId, addTime, currentTime, config.getReboundTimeWindow() * 15);
                            }
                            return timeout;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[{}] 检查监控超时异常: {}", instId, e.getMessage());
        }
        return false;
    }

    /**
     * 清理过期的Redis数据
     */
    private void cleanupExpiredData() {
        try {
            // 清理过期的监控候选
            List<String> monitoringCandidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            if (monitoringCandidates != null) {
                long currentTime = System.currentTimeMillis();
                long timeWindow = config.getReboundTimeWindow() * 15 * 60 * 1000L;

                List<String> validCandidates = new ArrayList<>();
                for (String candidate : monitoringCandidates) {
                    String[] parts = candidate.split(":");
                    if (parts.length >= 2) {
                        try {
                            long addTime = Long.parseLong(parts[1]);
                            if ((currentTime - addTime) <= timeWindow) {
                                validCandidates.add(candidate);
                            } else {
                                logDebug("清理过期监控候选: {}", parts[0]);
                            }
                        } catch (NumberFormatException e) {
                            // 忽略格式错误的数据
                        }
                    }
                }

                if (validCandidates.size() != monitoringCandidates.size()) {
                    RedisUtils.setCacheList(MONITORING_CANDIDATES_KEY, validCandidates);
                    log.info("清理了{}个过期的监控候选", monitoringCandidates.size() - validCandidates.size());
                }
            }
        } catch (Exception e) {
            log.error("清理过期数据异常", e);
        }
    }

    /**
     * 获取Redis统计信息
     */
    private void logRedisStats() {
        try {
            List<String> downtrendCandidates = RedisUtils.getCacheList(DOWNTREND_CANDIDATES_KEY);
            List<String> monitoringCandidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            List<String> processedToday = RedisUtils.getCacheList(PROCESSED_KEY);

            int downtrendCount = downtrendCandidates != null ? downtrendCandidates.size() : 0;
            int monitoringCount = monitoringCandidates != null ? monitoringCandidates.size() : 0;
            int processedCount = processedToday != null ? processedToday.size() : 0;

            log.info("Redis统计 - 下跌趋势候选: {}, 监控中: {}, 今日已处理: {}",
                downtrendCount, monitoringCount, processedCount);
        } catch (Exception e) {
            log.error("获取Redis统计信息异常", e);
        }
    }

    /**
     * 止盈止损价格内部类
     */
    public static class StopLossAndTakeProfitPrices {
        public final BigDecimal stopLossPrice;
        public final BigDecimal takeProfitPrice;

        public StopLossAndTakeProfitPrices(BigDecimal stopLossPrice, BigDecimal takeProfitPrice) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice = takeProfitPrice;
        }

        @Override
        public String toString() {
            return String.format("StopLoss: %s, TakeProfit: %s", stopLossPrice, takeProfitPrice);
        }
    }
}
