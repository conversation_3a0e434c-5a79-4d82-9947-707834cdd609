package com.miner.strategy;

import cn.hutool.core.thread.ThreadUtil;
import com.miner.strategy.config.EMAReversalShortConfig;
import com.miner.strategy.exception.StrategyExceptionHandler;
import com.miner.strategy.monitor.StrategyPerformanceMonitor;
import com.miner.strategy.util.EMASignalUtil;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import static com.miner.strategy.util.EMASignalUtil.calculateATR;

/**
 * EMA反转做空策略
 * <p>
 * 策略逻辑：
 * 1. 前100根15分钟K线中存在连续40根收盘价小于EMA55
 * 2. 在4小时内价格反弹到EMA55上方
 * 3. 最近5根K线ADX存在大于55的情况
 * 4. 前两根K线ADX连续下降
 * 5. 做空入场，止损4倍ATR，止盈16倍ATR
 *
 * <AUTHOR>
 */
@Component("emaReversalShortStrategy")
@Slf4j
public class EMAReversalShortStrategy extends BaseStrategy {

    @Resource
    private EMAReversalShortConfig config;

    @Resource
    private StrategyPerformanceMonitor performanceMonitor;

    @Resource
    private StrategyExceptionHandler exceptionHandler;

    private static final String STRATEGY_NAME = "EMAReversalShortStrategy";

    // 策略状态枚举
    private enum StrategyState {
        WAITING,      // 等待历史下跌趋势条件
        MONITORING,   // 监控反弹阶段
        READY,        // 准备入场
        POSITIONED    // 已持仓
    }

    @Override
    public void run() {
        if (!config.isEnabled()) {
            log.info("EMA反转做空策略已禁用，跳过执行");
            return;
        }

        log.info("==== EMA反转做空策略开始执行 ====");

        // 启动性能监控
        if (config.isEnablePerformanceMonitoring()) {
            performanceMonitor.startMonitoring(STRATEGY_NAME);
        }

        // 获取交易对列表
        List<String> instIds = getInstIds(config.getInstIdLimit());
        if (instIds.isEmpty()) {
            log.warn("未获取到交易对列表，策略结束");
            return;
        }

        log.info("获取到{}个交易对，开始处理", instIds.size());

        // 处理每个交易对
        for (String instId : instIds) {
            long processStartTime = 0;
            try {
                if (config.isEnablePerformanceMonitoring()) {
                    processStartTime = performanceMonitor.recordProcessStart(STRATEGY_NAME);
                }

                logProgress(instId);
                boolean hasSignal = processInstrument(instId);

                if (config.isEnablePerformanceMonitoring()) {
                    performanceMonitor.recordProcessComplete(STRATEGY_NAME, processStartTime, hasSignal);
                }

                ThreadUtil.sleep(config.getProcessingInterval());
            } catch (Exception e) {
                log.error("EMA反转做空策略处理交易对 {} 异常", instId, e);
                if (config.isEnablePerformanceMonitoring() && processStartTime > 0) {
                    performanceMonitor.recordProcessComplete(STRATEGY_NAME, processStartTime, false);
                }
            }
        }

        logFinalStats();
    }

    /**
     * 处理单个交易对
     */
    private boolean processInstrument(String instId) {
        // 检查是否已有仓位
        if (hasExistingTrade(instId)) {
            return false;
        }

        // 检查风险控制
        if (!checkRiskLimits(instId)) {
            return false;
        }

        // 获取K线数据（需要足够的历史数据）
        List<KLineEntity> klineData = getKlineData(instId);
        if (klineData == null || klineData.size() < config.getHistoryPeriod() + 20) {
            logDebug("[{}] K线数据不足，需要至少{}根，实际{}根",
                instId, config.getHistoryPeriod() + 20, klineData != null ? klineData.size() : 0);
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = extractClosePrices(klineData);

        // 步骤1：检查历史下跌趋势
        if (!checkHistoricalDowntrend(instId, klineData, closePrices)) {
            return false;
        }

        // 步骤2：检查反弹条件
        if (!checkRebound(instId, klineData, closePrices)) {
            return false;
        }

        // 步骤3：检查ADX条件
        if (!checkADXConditions(instId, klineData)) {
            return false;
        }

        // 步骤4：执行其他过滤器
        if (!passAllFilters(instId, klineData)) {
            return false;
        }

        // 步骤5：执行入场
        handleEntry(instId, klineData);
        return true;
    }

    /**
     * 检查历史下跌趋势
     * 前100根K线中存在连续40根收盘价小于EMA55
     */
    private boolean checkHistoricalDowntrend(String instId, List<KLineEntity> klineData, List<Double> closePrices) {
        logDebug("[{}] 开始检查历史下跌趋势...", instId);

        int consecutiveCount = 0;
        int maxConsecutive = 0;

        // 检查前100根K线
        int checkPeriod = Math.min(config.getHistoryPeriod(), klineData.size() - 20);

        // 从第20根开始，保留最新20根用于其他判断
        for (int i = 20; i < 20 + checkPeriod; i++) {
            double closePrice = klineData.get(i).getClosePrice().doubleValue();
            double ema55 = calculateEMA(closePrices, i, config.getEmaPeriod());

            if (closePrice < ema55) {
                consecutiveCount++;
                maxConsecutive = Math.max(maxConsecutive, consecutiveCount);
            } else {
                consecutiveCount = 0;
            }
        }

        boolean hasDowntrend = maxConsecutive >= config.getConsecutiveDownBars();

        logDebug("[{}] 历史下跌趋势检查 - 最大连续下跌: {}, 要求: {}, 结果: {}",
            instId, maxConsecutive, config.getConsecutiveDownBars(), hasDowntrend ? "通过" : "未通过");

        return hasDowntrend;
    }

    /**
     * 检查反弹条件
     * 在4小时内价格反弹到EMA55上方
     */
    private boolean checkRebound(String instId, List<KLineEntity> klineData, List<Double> closePrices) {
        logDebug("[{}] 开始检查反弹条件...", instId);

        // 当前价格和EMA55
        double currentPrice = klineData.get(0).getClosePrice().doubleValue();
        double currentEMA55 = calculateEMA(closePrices, 0, config.getEmaPeriod());

        // 检查当前价格是否在EMA55上方
        if (currentPrice <= currentEMA55) {
            logDebug("[{}] 当前价格{}未站上EMA55{}", instId,
                String.format("%.4f", currentPrice), String.format("%.4f", currentEMA55));
            return false;
        }

        // 检查在反弹时间窗口内是否有价格低于EMA55的情况
        boolean hasRebound = false;
        int windowSize = Math.min(config.getReboundTimeWindow(), klineData.size());

        for (int i = 1; i < windowSize; i++) {
            double pastPrice = klineData.get(i).getClosePrice().doubleValue();
            double pastEMA55 = calculateEMA(closePrices, i, config.getEmaPeriod());

            if (pastPrice < pastEMA55) {
                // 找到了价格低于EMA55的情况，确认这是一个反弹
                double reboundPercent = (currentPrice - pastPrice) / pastPrice;
                if (reboundPercent >= config.getMinReboundPercent()) {
                    hasRebound = true;
                    logDebug("[{}] 发现有效反弹 - 从{}反弹至{}，涨幅{:.2f}%",
                        instId, String.format("%.4f", pastPrice),
                        String.format("%.4f", currentPrice), reboundPercent * 100);
                    break;
                }
            }
        }

        logDebug("[{}] 反弹条件检查 - 当前价格: {}, EMA55: {}, 结果: {}",
            instId, String.format("%.4f", currentPrice), String.format("%.4f", currentEMA55),
            hasRebound ? "通过" : "未通过");

        return hasRebound;
    }

    /**
     * 检查ADX条件
     * 1. 最近5根K线ADX存在大于55的情况
     * 2. 前两根K线ADX连续下降
     */
    private boolean checkADXConditions(String instId, List<KLineEntity> klineData) {
        logDebug("[{}] 开始检查ADX条件...", instId);

        try {
            // 检查最近5根K线ADX是否有大于阈值的
            boolean hasHighADX = false;
            double maxADX = 0;

            for (int i = 0; i < config.getAdxCheckPeriod(); i++) {
                double adx = calculateADX(klineData, i, config.getAdxPeriod());
                maxADX = Math.max(maxADX, adx);
                if (adx > config.getAdxThreshold()) {
                    hasHighADX = true;
                    logDebug("[{}] 第{}根K线ADX为{:.2f}，超过阈值{}",
                        instId, i, adx, config.getAdxThreshold());
                }
            }

            if (!hasHighADX) {
                logDebug("[{}] 最近{}根K线ADX最大值{:.2f}，未超过阈值{}",
                    instId, config.getAdxCheckPeriod(), maxADX, config.getAdxThreshold());
                return false;
            }

            // 检查前两根K线ADX连续下降
            double adx1 = calculateADX(klineData, 1, config.getAdxPeriod());
            double adx2 = calculateADX(klineData, 2, config.getAdxPeriod());

            boolean isDecreasing = adx1 < adx2;

            logDebug("[{}] ADX下降检查 - ADX[1]: {:.2f}, ADX[2]: {:.2f}, 下降: {}",
                instId, adx1, adx2, isDecreasing ? "是" : "否");

            return isDecreasing;

        } catch (Exception e) {
            log.error("[{}] ADX条件检查异常: {}", instId, e.getMessage());
            return false;
        }
    }

    /**
     * 获取K线数据
     */
    private List<KLineEntity> getKlineData(String instId) {
        try {
            // 获取足够的15分钟K线数据
            int requiredSize = config.getHistoryPeriod() + 50; // 多获取一些数据确保计算准确
            return getKline(instId, "15m", String.valueOf(requiredSize), false);
        } catch (Exception e) {
            log.error("[{}] 获取K线数据异常: {}", instId, e.getMessage());
            return null;
        }
    }

    /**
     * 提取收盘价列表
     */
    private List<Double> extractClosePrices(List<KLineEntity> klineData) {
        return klineData.stream()
            .map(k -> k.getClosePrice().doubleValue())
            .collect(Collectors.toList());
    }

    /**
     * 计算EMA
     */
    private double calculateEMA(List<Double> prices, int offset, int period) {
        return exceptionHandler.safeCalculate(
            "calculateEMA_" + period + "_" + offset,
            () -> {
                exceptionHandler.validateListParameter(prices, "prices", "calculateEMA", period + offset);
                exceptionHandler.validateNumericParameter(period, "period", "calculateEMA");
                return EMASignalUtil.calculateEMA(prices, offset, period);
            },
            0.0
        );
    }

    /**
     * 计算ADX指标
     */
    private double calculateADX(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < period + offset + 1) {
            return 0.0;
        }

        // 计算+DI和-DI
        double[] diValues = calculateDI(klineList, offset, period);
        double plusDI = diValues[0];
        double minusDI = diValues[1];

        // 计算方向指数DX
        double dx = 0.0;
        if (plusDI + minusDI > 0) {
            dx = 100 * Math.abs(plusDI - minusDI) / (plusDI + minusDI);
        }

        // 计算ADX（DX的平滑平均）
        double adx = dx;
        for (int i = 1; i < period; i++) {
            if (klineList.size() <= offset + i + period) {
                break;
            }
            double[] prevDI = calculateDI(klineList, offset + i, period);
            double prevPlusDI = prevDI[0];
            double prevMinusDI = prevDI[1];

            double prevDX = 0.0;
            if (prevPlusDI + prevMinusDI > 0) {
                prevDX = 100 * Math.abs(prevPlusDI - prevMinusDI) / (prevPlusDI + prevMinusDI);
            }

            adx = ((period - 1) * adx + prevDX) / period;
        }

        return adx;
    }

    /**
     * 计算方向指标DI
     *
     * @return double数组，[0]=+DI，[1]=-DI
     */
    private double[] calculateDI(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < period + offset + 1) {
            return new double[]{0.0, 0.0};
        }

        double sumTR = 0.0;
        double sumPlusDM = 0.0;
        double sumMinusDM = 0.0;

        for (int i = offset; i < offset + period; i++) {
            KLineEntity current = klineList.get(i);
            KLineEntity previous = klineList.get(i + 1);

            double high = current.getHigh().doubleValue();
            double low = current.getLow().doubleValue();
            double prevHigh = previous.getHigh().doubleValue();
            double prevLow = previous.getLow().doubleValue();
            double prevClose = previous.getClose().doubleValue();

            // 计算TR (True Range)
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(Math.max(tr1, tr2), tr3);
            sumTR += tr;

            // 计算DM (Directional Movement)
            double upMove = high - prevHigh;
            double downMove = prevLow - low;

            if (upMove > downMove && upMove > 0) {
                sumPlusDM += upMove;
            }
            if (downMove > upMove && downMove > 0) {
                sumMinusDM += downMove;
            }
        }

        if (sumTR == 0) {
            return new double[]{0.0, 0.0};
        }

        double plusDI = (sumPlusDM / sumTR) * 100;
        double minusDI = (sumMinusDM / sumTR) * 100;

        return new double[]{plusDI, minusDI};
    }

    /**
     * 执行所有过滤器检查
     */
    private boolean passAllFilters(String instId, List<KLineEntity> klineData) {
        // 成交量过滤
        if (config.isEnableVolumeFilter() && !checkVolumeFilter(instId, klineData)) {
            return false;
        }

        // 波动率过滤
        if (config.isEnableVolatilityFilter() && !checkVolatilityFilter(instId, klineData)) {
            return false;
        }

        // 时间过滤
        if (config.isEnableTimeFilter() && !checkTimeFilter(instId)) {
            return false;
        }

        return true;
    }

    /**
     * 检查成交量过滤
     */
    private boolean checkVolumeFilter(String instId, List<KLineEntity> klineData) {
        try {
            double currentVolume = klineData.get(0).getVolume();
            double avgVolume = klineData.stream()
                .limit(5)
                .mapToDouble(KLineEntity::getVolume)
                .average()
                .orElse(0);

            boolean result = currentVolume > avgVolume * config.getVolumeMultiplier();

            logDebug("[{}] 成交量过滤 - 当前: {}, 平均: {}, 倍数要求: {}, 结果: {}",
                instId, String.format("%.2f", currentVolume), String.format("%.2f", avgVolume),
                config.getVolumeMultiplier(), result ? "通过" : "未通过");

            return result;
        } catch (Exception e) {
            log.error("[{}] 成交量过滤检查异常: {}", instId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查波动率过滤
     */
    private boolean checkVolatilityFilter(String instId, List<KLineEntity> klineData) {
        try {
            double currentATR = calculateATR(klineData, 0, config.getAtrPeriod());
            double avgATR = 0;

            // 计算历史平均ATR
            for (int i = 1; i <= 20; i++) {
                avgATR += calculateATR(klineData, i, config.getAtrPeriod());
            }
            avgATR /= 20;

            boolean result = currentATR <= avgATR * config.getMaxVolatilityMultiplier();

            logDebug("[{}] 波动率过滤 - 当前ATR: {}, 平均ATR: {}, 最大倍数: {}, 结果: {}",
                instId, String.format("%.6f", currentATR), String.format("%.6f", avgATR),
                config.getMaxVolatilityMultiplier(), result ? "通过" : "未通过");

            return result;
        } catch (Exception e) {
            log.error("[{}] 波动率过滤检查异常: {}", instId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查时间过滤
     */
    private boolean checkTimeFilter(String instId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int currentHour = now.getHour();

            for (int forbiddenHour : config.getForbiddenHours()) {
                if (currentHour == forbiddenHour) {
                    logDebug("[{}] 时间过滤 - 当前时间{}点在禁止交易时段内", instId, currentHour);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("[{}] 时间过滤检查异常: {}", instId, e.getMessage());
            return true; // 异常时允许交易
        }
    }

    /**
     * 检查风险限制
     */
    private boolean checkRiskLimits(String instId) {
        // 这里可以添加各种风险控制逻辑
        // 例如：检查连续亏损次数、日内交易次数等
        return true;
    }

    /**
     * 检查是否已有仓位
     */
    private boolean hasExistingTrade(String instId) {
        return true;
    }

    /**
     * 处理入场逻辑
     */
    private void handleEntry(String instId, List<KLineEntity> klineData) {
        try {
            BigDecimal currentPrice = klineData.get(0).getClosePrice();
            log.info("[{}] 满足所有入场条件，准备做空入场，当前价格: {}", instId, currentPrice);

            if (config.isSimulationMode()) {
                log.info("[{}] 模拟模式 - 做空信号生成，价格: {}", instId, currentPrice);
                return;
            }

            // 计算ATR
            double atr = calculateATR(klineData, 0, config.getAtrPeriod());
            if (atr <= 0) {
                log.warn("[{}] ATR计算错误，取消入场", instId);
                return;
            }

            // 计算止盈止损价格
            StopLossAndTakeProfitPrices prices = calculateStopLossAndTakeProfitPrices(currentPrice, atr);

            // 获取下单数量
            String sz = getSz(instId, currentPrice.doubleValue());

            log.info("[{}] 入场参数 - 方向: 做空, 数量: {}, 止损: {}, 止盈: {}, ATR: {}",
                instId, sz, prices.stopLossPrice, prices.takeProfitPrice, String.format("%.6f", atr));

            // 执行开仓
            log.info("[{}] 执行做空开仓操作，数量: {}", instId, sz);
            trade(instId, "sell", sz, "short", false);
            log.info("[{}] 开仓指令已发送，等待仓位更新", instId);
            Thread.sleep(200); // 等待仓位更新

            // 设置止盈止损
            setupStopOrders(instId, sz, prices);

            // 记录交易信息到Redis
            recordTradeToRedis(instId);

            // 发送通知
            if (config.isEnablePushNotification()) {
                sendTradingNotification(instId, "做空", currentPrice, prices, sz);
            }

        } catch (Exception e) {
            log.error("[{}] 执行入场操作异常", instId, e);
        }
    }

    /**
     * 计算止盈止损价格
     */
    private StopLossAndTakeProfitPrices calculateStopLossAndTakeProfitPrices(BigDecimal currentPrice, double atr) {
        double price = currentPrice.doubleValue();

        // 做空：止损在上方，止盈在下方
        double stopLossPrice = price + (atr * config.getStopLossMultiplier());
        double takeProfitPrice = price - (atr * config.getTakeProfitMultiplier());

        // 保持价格精度
        int scale = currentPrice.scale();

        return new StopLossAndTakeProfitPrices(
            new BigDecimal(stopLossPrice).setScale(scale, RoundingMode.HALF_UP),
            new BigDecimal(takeProfitPrice).setScale(scale, RoundingMode.HALF_UP)
        );
    }

    /**
     * 设置止盈止损订单
     */
    private void setupStopOrders(String instId, String sz, StopLossAndTakeProfitPrices prices) {
        try {
            // 做空的平仓方向是买入
            String orderSide = "buy";
            String posSide = "short";

            // 设置止损单
            log.info("[{}] 设置止损单 - 方向: {}, 价格: {}, 数量: {}",
                instId, orderSide, prices.stopLossPrice, sz);
            algoTradeLoss(instId, orderSide, sz, posSide, prices.stopLossPrice.toString());

            // 设置止盈单
            log.info("[{}] 设置止盈单 - 方向: {}, 价格: {}, 数量: {}",
                instId, orderSide, prices.takeProfitPrice, sz);
            algoTradeWin(instId, orderSide, sz, posSide, prices.takeProfitPrice.toString());

        } catch (Exception e) {
            log.error("[{}] 设置止盈止损订单异常", instId, e);
        }
    }

    /**
     * 记录交易信息到Redis
     */
    private void recordTradeToRedis(String instId) {
        if (config.isSaveSignalsToRedis()) {
            try {
                String key = "ema_reversal_short_signal:" + instId;
                String value = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                // 这里需要使用RedisUtils保存信号
                // RedisUtils.setCacheObject(key, value, Duration.ofHours(config.getSignalExpirationHours()));
                logDebug("[{}] 交易信号已保存到Redis", instId);
            } catch (Exception e) {
                log.error("[{}] 保存交易信号到Redis异常: {}", instId, e.getMessage());
            }
        }
    }

    /**
     * 发送交易通知
     */
    private void sendTradingNotification(String instId, String direction, BigDecimal entryPrice,
                                         StopLossAndTakeProfitPrices prices, String size) {
        try {
            String message = String.format(
                "🔥 EMA反转做空策略信号\n" +
                    "交易对: %s\n" +
                    "方向: %s\n" +
                    "入场价: %s\n" +
                    "止损价: %s\n" +
                    "止盈价: %s\n" +
                    "数量: %s\n" +
                    "时间: %s",
                instId, direction, entryPrice, prices.stopLossPrice, prices.takeProfitPrice, size,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            // 这里需要调用推送服务
            // pushPlusService.sendMessage("EMA反转做空策略", message);
            log.info("[{}] 交易通知已发送", instId);
        } catch (Exception e) {
            log.error("[{}] 发送交易通知异常: {}", instId, e.getMessage());
        }
    }

    /**
     * 记录处理进度
     */
    private void logProgress(String instId) {
        if (config.isEnableDetailedLogging()) {
            log.debug("开始处理交易对: {}", instId);
        }
    }

    /**
     * 记录最终统计
     */
    private void logFinalStats() {
        if (config.isEnablePerformanceMonitoring()) {
            log.info("==== EMA反转做空策略执行完成 ====");
            // 这里可以添加性能统计信息
        }
    }

    /**
     * 调试日志
     */
    private void logDebug(String format, Object... args) {
        if (config.isEnableDetailedLogging()) {
            log.debug(format, args);
        }
    }

    /**
     * 止盈止损价格内部类
     */
    public static class StopLossAndTakeProfitPrices {
        public final BigDecimal stopLossPrice;
        public final BigDecimal takeProfitPrice;

        public StopLossAndTakeProfitPrices(BigDecimal stopLossPrice, BigDecimal takeProfitPrice) {
            this.stopLossPrice = stopLossPrice;
            this.takeProfitPrice = takeProfitPrice;
        }

        @Override
        public String toString() {
            return String.format("StopLoss: %s, TakeProfit: %s", stopLossPrice, takeProfitPrice);
        }
    }
}
