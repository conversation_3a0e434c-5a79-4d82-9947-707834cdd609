package com.miner.strategy.util;

import com.miner.common.utils.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EMA反转做空策略Redis管理工具
 * 提供候选列表的查看、管理和统计功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EMAReversalShortRedisManager {

    // Redis键名常量
    private static final String DOWNTREND_CANDIDATES_KEY = "ema_reversal_short:downtrend_candidates";
    private static final String MONITORING_CANDIDATES_KEY = "ema_reversal_short:monitoring_candidates";
    private static final String PROCESSED_TODAY_KEY = "ema_reversal_short:processed_today";

    /**
     * 获取下跌趋势候选列表
     */
    public List<String> getDowntrendCandidates() {
        try {
            List<String> candidates = RedisUtils.getCacheList(DOWNTREND_CANDIDATES_KEY);
            return candidates != null ? candidates : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取下跌趋势候选列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取监控候选列表（包含时间戳）
     */
    public List<String> getMonitoringCandidatesRaw() {
        try {
            List<String> candidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            return candidates != null ? candidates : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取监控候选列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取监控候选列表（仅交易对名称）
     */
    public List<String> getMonitoringCandidates() {
        return getMonitoringCandidatesRaw().stream()
            .map(candidate -> candidate.split(":")[0])
            .collect(Collectors.toList());
    }

    /**
     * 获取监控候选详细信息
     */
    public List<MonitoringCandidate> getMonitoringCandidatesWithDetails() {
        List<MonitoringCandidate> details = new ArrayList<>();
        List<String> rawCandidates = getMonitoringCandidatesRaw();
        
        for (String candidate : rawCandidates) {
            String[] parts = candidate.split(":");
            if (parts.length >= 2) {
                try {
                    String instId = parts[0];
                    long addTime = Long.parseLong(parts[1]);
                    LocalDateTime addDateTime = LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(addTime), 
                        java.time.ZoneId.systemDefault()
                    );
                    
                    long currentTime = System.currentTimeMillis();
                    long elapsedMinutes = (currentTime - addTime) / (60 * 1000);
                    
                    details.add(new MonitoringCandidate(instId, addDateTime, elapsedMinutes));
                } catch (NumberFormatException e) {
                    log.warn("解析监控候选时间戳异常: {}", candidate);
                }
            }
        }
        
        return details;
    }

    /**
     * 获取今日已处理列表
     */
    public List<String> getProcessedToday() {
        try {
            List<String> processed = RedisUtils.getCacheList(PROCESSED_TODAY_KEY);
            return processed != null ? processed : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取今日已处理列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 手动添加下跌趋势候选
     */
    public boolean addDowntrendCandidate(String instId) {
        try {
            RedisUtils.setCacheListValue(DOWNTREND_CANDIDATES_KEY, instId);
            RedisUtils.expire(DOWNTREND_CANDIDATES_KEY, Duration.ofDays(7));
            log.info("手动添加下跌趋势候选: {}", instId);
            return true;
        } catch (Exception e) {
            log.error("手动添加下跌趋势候选异常: {}", instId, e);
            return false;
        }
    }

    /**
     * 手动移除下跌趋势候选
     */
    public boolean removeDowntrendCandidate(String instId) {
        try {
            List<String> candidates = RedisUtils.getCacheList(DOWNTREND_CANDIDATES_KEY);
            if (candidates != null && candidates.remove(instId)) {
                RedisUtils.setCacheList(DOWNTREND_CANDIDATES_KEY, candidates);
                log.info("手动移除下跌趋势候选: {}", instId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("手动移除下跌趋势候选异常: {}", instId, e);
            return false;
        }
    }

    /**
     * 手动添加监控候选
     */
    public boolean addMonitoringCandidate(String instId) {
        try {
            String candidateWithTimestamp = instId + ":" + System.currentTimeMillis();
            RedisUtils.setCacheListValue(MONITORING_CANDIDATES_KEY, candidateWithTimestamp);
            RedisUtils.expire(MONITORING_CANDIDATES_KEY, Duration.ofHours(24));
            log.info("手动添加监控候选: {}", instId);
            return true;
        } catch (Exception e) {
            log.error("手动添加监控候选异常: {}", instId, e);
            return false;
        }
    }

    /**
     * 手动移除监控候选
     */
    public boolean removeMonitoringCandidate(String instId) {
        try {
            List<String> candidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            if (candidates != null) {
                boolean removed = candidates.removeIf(candidate -> candidate.startsWith(instId + ":"));
                if (removed) {
                    RedisUtils.setCacheList(MONITORING_CANDIDATES_KEY, candidates);
                    log.info("手动移除监控候选: {}", instId);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("手动移除监控候选异常: {}", instId, e);
            return false;
        }
    }

    /**
     * 清空所有候选列表
     */
    public void clearAllCandidates() {
        try {
            RedisUtils.deleteObject(DOWNTREND_CANDIDATES_KEY);
            RedisUtils.deleteObject(MONITORING_CANDIDATES_KEY);
            RedisUtils.deleteObject(PROCESSED_TODAY_KEY);
            log.info("已清空所有候选列表");
        } catch (Exception e) {
            log.error("清空候选列表异常", e);
        }
    }

    /**
     * 清理过期的监控候选
     */
    public int cleanupExpiredMonitoringCandidates(int timeWindowMinutes) {
        try {
            List<String> candidates = RedisUtils.getCacheList(MONITORING_CANDIDATES_KEY);
            if (candidates == null || candidates.isEmpty()) {
                return 0;
            }
            
            long currentTime = System.currentTimeMillis();
            long timeWindow = timeWindowMinutes * 60 * 1000L;
            
            List<String> validCandidates = new ArrayList<>();
            int expiredCount = 0;
            
            for (String candidate : candidates) {
                String[] parts = candidate.split(":");
                if (parts.length >= 2) {
                    try {
                        long addTime = Long.parseLong(parts[1]);
                        if ((currentTime - addTime) <= timeWindow) {
                            validCandidates.add(candidate);
                        } else {
                            expiredCount++;
                            log.debug("清理过期监控候选: {}", parts[0]);
                        }
                    } catch (NumberFormatException e) {
                        expiredCount++;
                    }
                }
            }
            
            if (expiredCount > 0) {
                RedisUtils.setCacheList(MONITORING_CANDIDATES_KEY, validCandidates);
                log.info("清理了{}个过期的监控候选", expiredCount);
            }
            
            return expiredCount;
        } catch (Exception e) {
            log.error("清理过期监控候选异常", e);
            return 0;
        }
    }

    /**
     * 获取统计信息
     */
    public CandidateStats getStats() {
        int downtrendCount = getDowntrendCandidates().size();
        int monitoringCount = getMonitoringCandidates().size();
        int processedCount = getProcessedToday().size();
        
        return new CandidateStats(downtrendCount, monitoringCount, processedCount);
    }

    /**
     * 打印统计信息
     */
    public void printStats() {
        CandidateStats stats = getStats();
        log.info("=== EMA反转做空策略Redis统计 ===");
        log.info("下跌趋势候选: {} 个", stats.downtrendCount);
        log.info("监控中候选: {} 个", stats.monitoringCount);
        log.info("今日已处理: {} 个", stats.processedCount);
        
        // 打印监控候选详情
        List<MonitoringCandidate> monitoringDetails = getMonitoringCandidatesWithDetails();
        if (!monitoringDetails.isEmpty()) {
            log.info("监控候选详情:");
            for (MonitoringCandidate detail : monitoringDetails) {
                log.info("  {} - 添加时间: {}, 已监控: {}分钟", 
                    detail.instId, 
                    detail.addTime.format(DateTimeFormatter.ofPattern("MM-dd HH:mm")), 
                    detail.elapsedMinutes);
            }
        }
    }

    /**
     * 监控候选详细信息
     */
    public static class MonitoringCandidate {
        public final String instId;
        public final LocalDateTime addTime;
        public final long elapsedMinutes;

        public MonitoringCandidate(String instId, LocalDateTime addTime, long elapsedMinutes) {
            this.instId = instId;
            this.addTime = addTime;
            this.elapsedMinutes = elapsedMinutes;
        }
    }

    /**
     * 候选统计信息
     */
    public static class CandidateStats {
        public final int downtrendCount;
        public final int monitoringCount;
        public final int processedCount;

        public CandidateStats(int downtrendCount, int monitoringCount, int processedCount) {
            this.downtrendCount = downtrendCount;
            this.monitoringCount = monitoringCount;
            this.processedCount = processedCount;
        }
    }
}
