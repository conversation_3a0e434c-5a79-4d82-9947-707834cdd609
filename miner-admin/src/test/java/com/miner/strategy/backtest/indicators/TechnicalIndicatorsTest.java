package com.miner.strategy.backtest.indicators;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 技术指标计算库测试
 * 验证TASK-005的实现质量和精度
 */
@DisplayName("技术指标计算库测试")
class TechnicalIndicatorsTest {

    private List<BigDecimal> testPrices;
    private List<BigDecimal> testHighs;
    private List<BigDecimal> testLows;
    private List<BigDecimal> testVolumes;

    @BeforeEach
    void setUp() {
        // 准备测试数据 - 模拟BTC价格数据
        testPrices = Arrays.asList(
            BigDecimal.valueOf(50000), BigDecimal.valueOf(49800), BigDecimal.valueOf(50200),
            BigDecimal.valueOf(50500), BigDecimal.valueOf(50300), BigDecimal.valueOf(50800),
            BigDecimal.valueOf(51000), BigDecimal.valueOf(50900), BigDecimal.valueOf(51200),
            BigDecimal.valueOf(51500), BigDecimal.valueOf(51300), BigDecimal.valueOf(51800),
            BigDecimal.valueOf(52000), BigDecimal.valueOf(51900), BigDecimal.valueOf(52200),
            BigDecimal.valueOf(52500), BigDecimal.valueOf(52300), BigDecimal.valueOf(52800),
            BigDecimal.valueOf(53000), BigDecimal.valueOf(52900), BigDecimal.valueOf(53200)
        );

        testHighs = Arrays.asList(
            BigDecimal.valueOf(50100), BigDecimal.valueOf(49900), BigDecimal.valueOf(50300),
            BigDecimal.valueOf(50600), BigDecimal.valueOf(50400), BigDecimal.valueOf(50900),
            BigDecimal.valueOf(51100), BigDecimal.valueOf(51000), BigDecimal.valueOf(51300),
            BigDecimal.valueOf(51600), BigDecimal.valueOf(51400), BigDecimal.valueOf(51900),
            BigDecimal.valueOf(52100), BigDecimal.valueOf(52000), BigDecimal.valueOf(52300),
            BigDecimal.valueOf(52600), BigDecimal.valueOf(52400), BigDecimal.valueOf(52900),
            BigDecimal.valueOf(53100), BigDecimal.valueOf(53000), BigDecimal.valueOf(53300)
        );

        testLows = Arrays.asList(
            BigDecimal.valueOf(49900), BigDecimal.valueOf(49700), BigDecimal.valueOf(50100),
            BigDecimal.valueOf(50400), BigDecimal.valueOf(50200), BigDecimal.valueOf(50700),
            BigDecimal.valueOf(50900), BigDecimal.valueOf(50800), BigDecimal.valueOf(51100),
            BigDecimal.valueOf(51400), BigDecimal.valueOf(51200), BigDecimal.valueOf(51700),
            BigDecimal.valueOf(51900), BigDecimal.valueOf(51800), BigDecimal.valueOf(52100),
            BigDecimal.valueOf(52400), BigDecimal.valueOf(52200), BigDecimal.valueOf(52700),
            BigDecimal.valueOf(52900), BigDecimal.valueOf(52800), BigDecimal.valueOf(53100)
        );

        testVolumes = Arrays.asList(
            BigDecimal.valueOf(1000), BigDecimal.valueOf(1200), BigDecimal.valueOf(800),
            BigDecimal.valueOf(1500), BigDecimal.valueOf(900), BigDecimal.valueOf(1300),
            BigDecimal.valueOf(1100), BigDecimal.valueOf(1400), BigDecimal.valueOf(1000),
            BigDecimal.valueOf(1600), BigDecimal.valueOf(1200), BigDecimal.valueOf(1800),
            BigDecimal.valueOf(1300), BigDecimal.valueOf(1700), BigDecimal.valueOf(1100),
            BigDecimal.valueOf(1900), BigDecimal.valueOf(1400), BigDecimal.valueOf(2000),
            BigDecimal.valueOf(1500), BigDecimal.valueOf(2100), BigDecimal.valueOf(1600)
        );
    }

    @Test
    @DisplayName("测试EMA计算精度")
    void testEMACalculation() {
        double ema9 = TechnicalIndicators.calculateEMA(testPrices, 9);
        double ema21 = TechnicalIndicators.calculateEMA(testPrices, 21);
        
        assertTrue(ema9 > 0, "EMA9应该大于0");
        assertTrue(ema21 > 0, "EMA21应该大于0");
        assertTrue(ema9 > ema21, "短期EMA应该更接近当前价格");
        
        // 验证计算精度
        assertNotEquals(0.0, ema9);
        assertNotEquals(0.0, ema21);
    }

    @Test
    @DisplayName("测试SMA计算精度")
    void testSMACalculation() {
        double sma10 = TechnicalIndicators.calculateSMA(testPrices, 10);
        double sma20 = TechnicalIndicators.calculateSMA(testPrices, 20);
        
        assertTrue(sma10 > 0, "SMA10应该大于0");
        assertTrue(sma20 > 0, "SMA20应该大于0");
        
        // 手动验证SMA10计算
        double expectedSMA10 = testPrices.subList(0, 10).stream()
            .mapToDouble(BigDecimal::doubleValue)
            .average()
            .orElse(0.0);
        
        assertEquals(expectedSMA10, sma10, 0.01, "SMA10计算应该准确");
    }

    @Test
    @DisplayName("测试RSI计算")
    void testRSICalculation() {
        double rsi = TechnicalIndicators.calculateRSI(testPrices, 14);
        
        assertTrue(rsi >= 0 && rsi <= 100, "RSI应该在0-100范围内");
        assertNotEquals(50.0, rsi, "RSI不应该是默认值");
    }

    @Test
    @DisplayName("测试MACD计算")
    void testMACDCalculation() {
        double[] macd = TechnicalIndicators.calculateMACD(testPrices);
        
        assertEquals(3, macd.length, "MACD应该返回3个值");
        assertNotEquals(0.0, macd[0], "MACD线不应该为0");
        assertNotEquals(0.0, macd[1], "信号线不应该为0");
        assertEquals(macd[0] - macd[1], macd[2], 0.01, "柱状图应该等于MACD线减信号线");
    }

    @Test
    @DisplayName("测试ATR计算")
    void testATRCalculation() {
        double atr = TechnicalIndicators.calculateATR(testHighs, testLows, testPrices, 14);
        
        assertTrue(atr > 0, "ATR应该大于0");
        assertTrue(atr < 10000, "ATR应该在合理范围内");
    }

    @Test
    @DisplayName("测试布林带计算")
    void testBollingerBandsCalculation() {
        double[] bb = TechnicalIndicators.calculateBollingerBands(testPrices, 20, 2.0);
        
        assertEquals(3, bb.length, "布林带应该返回3个值");
        assertTrue(bb[0] > bb[1], "上轨应该大于中轨");
        assertTrue(bb[1] > bb[2], "中轨应该大于下轨");
        assertTrue(bb[0] > 0 && bb[1] > 0 && bb[2] > 0, "所有值应该大于0");
    }

    @Test
    @DisplayName("测试ADX计算")
    void testADXCalculation() {
        double adx = TechnicalIndicators.calculateADX(testHighs, testLows, testPrices, 14);
        
        assertTrue(adx >= 0 && adx <= 100, "ADX应该在0-100范围内");
    }

    @Test
    @DisplayName("测试随机指标计算")
    void testStochasticCalculation() {
        double[] stoch = TechnicalIndicators.calculateStochastic(testHighs, testLows, testPrices, 14, 3);
        
        assertEquals(2, stoch.length, "随机指标应该返回2个值");
        assertTrue(stoch[0] >= 0 && stoch[0] <= 100, "%K应该在0-100范围内");
        assertTrue(stoch[1] >= 0 && stoch[1] <= 100, "%D应该在0-100范围内");
    }

    @Test
    @DisplayName("测试威廉指标计算")
    void testWilliamsRCalculation() {
        double wr = TechnicalIndicators.calculateWilliamsR(testHighs, testLows, testPrices, 14);
        
        assertTrue(wr >= -100 && wr <= 0, "Williams %R应该在-100到0范围内");
    }

    @Test
    @DisplayName("测试CCI计算")
    void testCCICalculation() {
        double cci = TechnicalIndicators.calculateCCI(testHighs, testLows, testPrices, 20);
        
        assertNotEquals(0.0, cci, "CCI不应该为0");
        assertTrue(Math.abs(cci) < 1000, "CCI应该在合理范围内");
    }

    @Test
    @DisplayName("测试动量指标计算")
    void testMomentumCalculation() {
        double momentum = TechnicalIndicators.calculateMomentum(testPrices, 10);
        
        assertNotEquals(0.0, momentum, "动量不应该为0");
    }

    @Test
    @DisplayName("测试ROC计算")
    void testROCCalculation() {
        double roc = TechnicalIndicators.calculateROC(testPrices, 10);
        
        assertNotEquals(0.0, roc, "ROC不应该为0");
        assertTrue(Math.abs(roc) < 100, "ROC应该在合理范围内");
    }

    @Test
    @DisplayName("测试MFI计算")
    void testMFICalculation() {
        double mfi = TechnicalIndicators.calculateMFI(testHighs, testLows, testPrices, testVolumes, 14);
        
        assertTrue(mfi >= 0 && mfi <= 100, "MFI应该在0-100范围内");
    }

    @Test
    @DisplayName("测试缓存机制")
    void testCacheMechanism() {
        // 第一次计算
        long startTime = System.currentTimeMillis();
        double ema1 = TechnicalIndicators.calculateEMA(testPrices, 21);
        long firstCalculationTime = System.currentTimeMillis() - startTime;
        
        // 第二次计算（应该使用缓存）
        startTime = System.currentTimeMillis();
        double ema2 = TechnicalIndicators.calculateEMA(testPrices, 21);
        long secondCalculationTime = System.currentTimeMillis() - startTime;
        
        assertEquals(ema1, ema2, 0.0001, "缓存的结果应该相同");
        assertTrue(secondCalculationTime <= firstCalculationTime, "缓存应该提高性能");
    }

    @Test
    @DisplayName("测试增量计算优化")
    void testIncrementalCalculation() {
        double previousEMA = 50000.0;
        double currentPrice = 50500.0;
        int period = 21;
        
        double incrementalEMA = TechnicalIndicators.calculateEMAIncremental(previousEMA, currentPrice, period);
        
        assertTrue(incrementalEMA > previousEMA, "价格上涨时EMA应该增加");
        assertTrue(Math.abs(incrementalEMA - previousEMA) < 1000, "EMA变化应该平滑");
    }

    @Test
    @DisplayName("测试批量指标计算")
    void testMultipleIndicatorsCalculation() {
        TechnicalIndicators.IndicatorConfig config = new TechnicalIndicators.IndicatorConfig();
        
        Map<String, Object> results = TechnicalIndicators.calculateMultipleIndicators(
            testHighs, testLows, testPrices, testVolumes, config);
        
        assertFalse(results.isEmpty(), "应该返回计算结果");
        assertTrue(results.containsKey("EMA_FAST"), "应该包含快速EMA");
        assertTrue(results.containsKey("EMA_MID"), "应该包含中期EMA");
        assertTrue(results.containsKey("EMA_SLOW"), "应该包含慢速EMA");
        assertTrue(results.containsKey("RSI"), "应该包含RSI");
        assertTrue(results.containsKey("MACD_LINE"), "应该包含MACD线");
    }

    @Test
    @DisplayName("测试边界条件")
    void testBoundaryConditions() {
        // 测试空数据
        assertDoesNotThrow(() -> {
            TechnicalIndicators.calculateEMA(new ArrayList<>(), 10);
            TechnicalIndicators.calculateRSI(null, 14);
        });
        
        // 测试数据不足
        List<BigDecimal> shortData = Arrays.asList(BigDecimal.valueOf(100), BigDecimal.valueOf(101));
        double ema = TechnicalIndicators.calculateEMA(shortData, 10);
        assertEquals(0.0, ema, "数据不足时应该返回0");
    }

    @Test
    @DisplayName("测试指标统计信息")
    void testIndicatorStatistics() {
        String stats = TechnicalIndicators.getIndicatorStatistics();
        
        assertNotNull(stats, "统计信息不应该为空");
        assertTrue(stats.contains("17+"), "应该支持17+个指标");
    }
}
