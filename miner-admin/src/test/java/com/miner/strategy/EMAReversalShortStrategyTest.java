package com.miner.strategy;

import com.miner.strategy.config.EMAReversalShortConfig;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * EMA反转做空策略测试类
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class EMAReversalShortStrategyTest {

    @Resource
    private EMAReversalShortStrategy strategy;
    
    @Resource
    private EMAReversalShortConfig config;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        config.setEnabled(true);
        config.setSimulationMode(true); // 启用模拟模式
        config.setEnableDetailedLogging(true);
        config.setInstIdLimit(1); // 只测试一个交易对
    }

    @Test
    void testStrategyExecution() {
        log.info("开始测试EMA反转做空策略...");
        
        try {
            // 执行策略
            strategy.run();
            log.info("策略测试完成");
        } catch (Exception e) {
            log.error("策略测试异常", e);
        }
    }

    @Test
    void testConfigurationLoading() {
        log.info("测试配置加载...");
        
        log.info("策略启用状态: {}", config.isEnabled());
        log.info("历史周期: {}", config.getHistoryPeriod());
        log.info("连续下跌K线要求: {}", config.getConsecutiveDownBars());
        log.info("EMA周期: {}", config.getEmaPeriod());
        log.info("ADX阈值: {}", config.getAdxThreshold());
        log.info("止损倍数: {}", config.getStopLossMultiplier());
        log.info("止盈倍数: {}", config.getTakeProfitMultiplier());
        
        log.info("配置加载测试完成");
    }

    /**
     * 创建测试用的K线数据
     * 模拟一个符合策略条件的场景
     */
    private List<KLineEntity> createTestKLineData() {
        List<KLineEntity> klines = new ArrayList<>();
        
        // 创建120根K线数据用于测试
        for (int i = 0; i < 120; i++) {
            KLineEntity kline = new KLineEntity();
            
            // 模拟价格数据
            double basePrice = 50000.0; // 基础价格
            double price = basePrice - (i * 10); // 模拟下跌趋势
            
            // 如果是最近几根K线，模拟反弹
            if (i < 10) {
                price = basePrice - 1000 + (i * 50); // 模拟反弹
            }
            
            kline.setOpen(new BigDecimal(price));
            kline.setHigh(new BigDecimal(price + 50));
            kline.setLow(new BigDecimal(price - 50));
            kline.setClose(new BigDecimal(price));
            kline.setVolume(1000.0 + Math.random() * 500); // 随机成交量
            
            klines.add(kline);
        }
        
        return klines;
    }

    @Test
    void testHistoricalDowntrendDetection() {
        log.info("测试历史下跌趋势检测...");
        
        // 这里可以添加具体的历史下跌趋势检测测试
        // 由于方法是private，需要通过反射或者将方法改为package-private来测试
        
        log.info("历史下跌趋势检测测试完成");
    }

    @Test
    void testADXCalculation() {
        log.info("测试ADX计算...");
        
        List<KLineEntity> testData = createTestKLineData();
        
        // 这里可以添加ADX计算的测试
        // 验证ADX计算的正确性
        
        log.info("ADX计算测试完成");
    }

    @Test
    void testRiskManagement() {
        log.info("测试风险管理...");
        
        // 测试止盈止损价格计算
        BigDecimal entryPrice = new BigDecimal("50000.00");
        double atr = 100.0;
        
        // 计算预期的止损和止盈价格
        double expectedStopLoss = entryPrice.doubleValue() + (atr * config.getStopLossMultiplier());
        double expectedTakeProfit = entryPrice.doubleValue() - (atr * config.getTakeProfitMultiplier());
        
        log.info("入场价格: {}", entryPrice);
        log.info("ATR: {}", atr);
        log.info("预期止损价格: {}", expectedStopLoss);
        log.info("预期止盈价格: {}", expectedTakeProfit);
        
        log.info("风险管理测试完成");
    }

    @Test
    void testVolumeFilter() {
        log.info("测试成交量过滤...");
        
        List<KLineEntity> testData = createTestKLineData();
        
        // 测试成交量过滤逻辑
        if (!testData.isEmpty()) {
            double currentVolume = testData.get(0).getVolume();
            double avgVolume = testData.stream()
                .limit(5)
                .mapToDouble(KLineEntity::getVolume)
                .average()
                .orElse(0);
            
            boolean volumeCheck = currentVolume > avgVolume * config.getVolumeMultiplier();
            
            log.info("当前成交量: {}", currentVolume);
            log.info("平均成交量: {}", avgVolume);
            log.info("成交量倍数要求: {}", config.getVolumeMultiplier());
            log.info("成交量检查结果: {}", volumeCheck ? "通过" : "未通过");
        }
        
        log.info("成交量过滤测试完成");
    }

    @Test
    void testParameterValidation() {
        log.info("测试参数验证...");
        
        // 验证关键参数的合理性
        assert config.getHistoryPeriod() > 0 : "历史周期必须大于0";
        assert config.getConsecutiveDownBars() > 0 : "连续下跌K线数必须大于0";
        assert config.getConsecutiveDownBars() <= config.getHistoryPeriod() : "连续下跌K线数不能超过历史周期";
        assert config.getEmaPeriod() > 0 : "EMA周期必须大于0";
        assert config.getAdxThreshold() > 0 : "ADX阈值必须大于0";
        assert config.getStopLossMultiplier() > 0 : "止损倍数必须大于0";
        assert config.getTakeProfitMultiplier() > 0 : "止盈倍数必须大于0";
        assert config.getTakeProfitMultiplier() > config.getStopLossMultiplier() : "止盈倍数应该大于止损倍数";
        
        log.info("参数验证通过");
    }

    @Test
    void testStrategyStateTransition() {
        log.info("测试策略状态转换...");
        
        // 这里可以测试策略的状态转换逻辑
        // WAITING -> MONITORING -> READY -> POSITIONED
        
        log.info("策略状态转换测试完成");
    }
}
