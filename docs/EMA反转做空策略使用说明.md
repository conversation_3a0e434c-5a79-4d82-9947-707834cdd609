# EMA反转做空策略使用说明

## 策略概述

EMA反转做空策略是一个基于技术分析的量化交易策略，专门用于捕捉价格在长期下跌趋势后的技术性反弹机会。该策略通过分析EMA55、ADX等技术指标，在合适的时机进行做空操作。

## 策略逻辑

### 执行流程

策略采用两阶段执行模式，通过Redis管理候选交易对，提高执行效率：

1. **第一阶段：处理Redis候选列表**
   - 优先处理已识别的下跌趋势候选交易对
   - 检查监控中的交易对是否到达入场点位
   - 避免重复分析，提高效率

2. **第二阶段：扫描新的下跌趋势**
   - 扫描新的交易对，寻找下跌趋势
   - 将符合条件的交易对添加到Redis候选列表
   - 避免重复处理已分析的交易对

### 入场条件（必须全部满足）

1. **历史下跌趋势确认**
   - 前100根15分钟K线中存在连续40根收盘价小于EMA55
   - 确保存在明显的下跌趋势
   - 满足条件的交易对会被添加到Redis下跌趋势候选列表

2. **反弹确认**
   - 在4小时内（16根15分钟K线）价格反弹到EMA55上方
   - 反弹幅度至少0.5%
   - 满足条件的交易对会被移动到Redis监控候选列表

3. **ADX强度确认**
   - 最近5根K线中至少有一根ADX > 55
   - 确保趋势强度足够

4. **ADX转弱信号**
   - 前两根K线ADX连续下降
   - 预示趋势可能反转

5. **过滤器确认**
   - 成交量过滤：当前成交量 > 近5期平均成交量的80%
   - 波动率过滤：当前ATR ≤ 历史平均ATR的2倍
   - 时间过滤：避开指定的禁止交易时段

### 出场条件

- **止损**：入场价格 + 4倍ATR
- **止盈**：入场价格 - 16倍ATR
- **盈亏比**：1:4（理论上）

### Redis候选列表管理

- **下跌趋势候选列表**：存储已确认下跌趋势的交易对，有效期7天
- **监控候选列表**：存储已反弹的交易对，有效期24小时，包含时间戳
- **今日已处理列表**：避免重复扫描，每日重置

## 文件结构

```
miner-admin/src/main/java/com/miner/strategy/
├── EMAReversalShortStrategy.java              # 主策略类
├── config/
│   └── EMAReversalShortConfig.java           # 配置类
├── util/
│   └── EMAReversalShortRedisManager.java     # Redis管理工具
└── test/
    └── EMAReversalShortStrategyTest.java     # 测试类

miner-admin/src/main/resources/
└── application-ema-reversal-short.yml        # 配置文件

docs/
└── EMA反转做空策略使用说明.md               # 本文档
```

## 配置说明

### 主要参数

| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| `history-period` | 100 | 历史K线检查周期 |
| `consecutive-down-bars` | 40 | 连续下跌K线数量要求 |
| `ema-period` | 55 | EMA周期 |
| `rebound-time-window` | 16 | 反弹监控时间窗口（4小时） |
| `adx-threshold` | 55.0 | ADX强度阈值 |
| `stop-loss-multiplier` | 4.0 | 止损倍数 |
| `take-profit-multiplier` | 16.0 | 止盈倍数 |

### Redis管理参数

| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| `enable-redis-candidate-management` | true | 是否启用Redis候选列表管理 |
| `downtrend-candidates-expiration-days` | 7 | 下跌趋势候选列表过期时间（天） |
| `monitoring-candidates-expiration-hours` | 24 | 监控候选列表过期时间（小时） |
| `cleanup-expired-data-on-startup` | true | 是否在启动时清理过期数据 |

### 风险控制参数

| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| `position-size-percent` | 0.02 | 仓位大小（账户资金的2%） |
| `max-daily-trades` | 3 | 每日最大交易次数 |
| `max-drawdown-percent` | 0.10 | 最大回撤限制（10%） |
| `max-consecutive-losses` | 3 | 连续亏损次数限制 |

## 使用方法

### 1. 配置策略

编辑 `application-ema-reversal-short.yml` 文件，根据需要调整参数：

```yaml
strategy:
  ema-reversal-short:
    enabled: true                    # 启用策略
    simulation-mode: false           # 关闭模拟模式（实盘交易）
    enable-detailed-logging: true    # 启用详细日志
    # ... 其他参数
```

### 2. 启动策略

在Spring Boot应用中注入并运行策略：

```java
@Autowired
private EMAReversalShortStrategy strategy;

// 运行策略
strategy.run();
```

### 3. 监控策略

查看日志输出，监控策略运行状态：

```
[INFO] ==== EMA反转做空策略开始执行 ====
[INFO] 开始处理Redis中的候选交易对...
[INFO] 发现3个下跌趋势候选交易对
[INFO] 发现1个监控中的候选交易对
[INFO] 开始扫描新的下跌趋势候选交易对...
[INFO] 获取到20个交易对，开始扫描下跌趋势
[DEBUG] [BTC-USDT-SWAP] 发现下跌趋势，添加到候选列表
[INFO] Redis统计 - 下跌趋势候选: 4, 监控中: 1, 今日已处理: 20
...
```

### 4. Redis候选列表管理

使用Redis管理工具查看和管理候选列表：

```java
@Autowired
private EMAReversalShortRedisManager redisManager;

// 查看统计信息
redisManager.printStats();

// 获取候选列表
List<String> downtrendCandidates = redisManager.getDowntrendCandidates();
List<String> monitoringCandidates = redisManager.getMonitoringCandidates();

// 手动管理候选列表
redisManager.addDowntrendCandidate("BTC-USDT-SWAP");
redisManager.removeMonitoringCandidate("ETH-USDT-SWAP");

// 清理过期数据
redisManager.cleanupExpiredMonitoringCandidates(240); // 4小时
```

## 测试

运行测试类验证策略功能：

```bash
mvn test -Dtest=EMAReversalShortStrategyTest
```

测试包括：
- 配置加载测试
- 历史下跌趋势检测测试
- ADX计算测试
- 风险管理测试
- 成交量过滤测试
- 参数验证测试

## 注意事项

### 1. 风险提示

- 该策略为做空策略，在牛市中可能表现不佳
- 需要充足的历史数据支持（至少120根K线）
- 对参数设置较为敏感，建议先进行充分测试
- 必须严格执行风险管理规则

### 2. 使用建议

- **首次使用**：建议先启用模拟模式（`simulation-mode: true`）进行测试
- **参数调优**：根据不同市场环境调整参数，特别是ADX阈值和止盈止损倍数
- **资金管理**：建议单笔交易风险不超过账户资金的2%
- **监控频率**：建议每15分钟检查一次策略运行状态

### 3. 性能优化

- 启用性能监控（`enable-performance-monitoring: true`）
- 合理设置交易对数量限制（`inst-id-limit`）
- 根据服务器性能调整处理间隔（`processing-interval`）

## 故障排除

### 常见问题

1. **策略不执行**
   - 检查 `enabled` 是否为 `true`
   - 检查是否有足够的历史数据
   - 查看日志中的错误信息

2. **没有交易信号**
   - 检查市场是否满足策略条件
   - 调整参数设置，如降低ADX阈值
   - 启用详细日志查看具体原因

3. **频繁止损**
   - 检查止损倍数设置是否过小
   - 考虑添加更多过滤条件
   - 检查市场波动率是否过高

### 日志级别

```yaml
logging:
  level:
    com.miner.strategy.EMAReversalShortStrategy: DEBUG  # 详细日志
    # 或
    com.miner.strategy.EMAReversalShortStrategy: INFO   # 基础日志
```

## 版本历史

- **v1.0** (2024-01-XX)
  - 初始版本
  - 实现基础策略逻辑
  - 支持配置化参数
  - 集成风险管理

## 联系方式

如有问题或建议，请联系开发团队。
