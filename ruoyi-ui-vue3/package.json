{"name": "ruoyi-vue-plus", "version": "1.0.0", "description": "RuoYi-Vue-Plus后台管理系统", "author": "LionLi", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/RuoYi-Vue-Plus-UI.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "echarts": "5.4.0", "element-plus": "2.2.27", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "nprogress": "0.2.0", "pinia": "2.0.22", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "unplugin-vue-setup-extend-plus": "0.4.9"}}