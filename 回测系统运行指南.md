# 回测系统运行指南

## 📋 概述

本指南将帮助你快速上手使用EMA回调策略回测系统，包括单次回测、参数优化、快速测试等多种运行方式。

## 🚀 快速开始

### 方式一：使用BacktestRunner（推荐）

这是最简单的运行方式，提供了多种预配置的回测模式。

#### 1. 编译项目
```bash
cd miner-admin
mvn compile
```

#### 2. 运行回测
```bash
# 运行单次回测（默认模式）
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.BacktestRunner"

# 或指定具体模式
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.BacktestRunner" -Dexec.args="single"

# 运行参数优化
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.BacktestRunner" -Dexec.args="optimization"

# 运行快速测试
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.BacktestRunner" -Dexec.args="quick"

# 运行系统演示
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.BacktestRunner" -Dexec.args="demo"
```

### 方式二：直接使用EMARetraceStrategyBacktest

```bash
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.EMARetraceStrategyBacktest"
```

### 方式三：使用新框架示例

```bash
mvn exec:java -Dexec.mainClass="com.miner.strategy.backtest.example.NewFrameworkExample"
```

## 🔧 运行模式详解

### 1. 单次回测 (single)

**用途**: 使用固定参数运行完整回测，获得详细的策略表现报告

**特点**:
- 回测时间范围：最近6个月
- 交易对：BTC-USDT-SWAP, ETH-USDT-SWAP
- 初始资金：10,000 USDT
- 详细的性能指标输出

**输出示例**:
```
==================================================
回测详细结果
==================================================
策略名称: EMARetraceStrategy
回测期间: 2024-06-01T00:00 至 2024-12-01T00:00
交易对: BTC-USDT-SWAP, ETH-USDT-SWAP
初始资金: 10000.00 USDT
最终资金: 12500.00 USDT
总收益: 2500.00 USDT
收益率: 25.00%
年化收益率: 50.00%
最大回撤: 8.50%
总交易次数: 45
盈利交易: 28 (62.2%)
亏损交易: 17 (37.8%)
盈亏比: 1.85
夏普比率: 1.42
索提诺比率: 2.15
==================================================
```

### 2. 参数优化 (optimization)

**用途**: 自动测试多组参数组合，找出最优参数设置

**特点**:
- 并行测试多个参数组合
- 按收益率和胜率排序
- 输出最佳参数组合

**优化参数范围**:
- EMA快线: 9, 12
- EMA中线: 21, 26  
- EMA慢线: 50, 55
- 止损倍数: 2.0, 3.0
- 止盈倍数: 8.0, 10.0

### 3. 快速测试 (quick)

**用途**: 快速验证系统功能，适合开发和调试

**特点**:
- 回测时间范围：最近2周
- 单一交易对：BTC-USDT-SWAP
- 快速输出简要结果

### 4. 系统演示 (demo)

**用途**: 展示系统各种功能，适合新用户了解系统能力

**包含**:
- BTC单独回测演示
- ETH单独回测演示  
- 多币种组合回测演示

## ⚙️ 配置说明

### 主要配置参数

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| startDate | 回测开始时间 | 6个月前 | 1个月-2年前 |
| endDate | 回测结束时间 | 当前时间 | 当前时间 |
| symbols | 交易对列表 | BTC,ETH | 任意支持的交易对 |
| initialCapital | 初始资金(USDT) | 10000 | 1000-100000 |
| positionSizeRatio | 单次开仓比例 | 0.02 (2%) | 0.01-0.1 |
| mainTimeframe | 主时间周期 | 4h | 1h, 4h, 1d |
| emaFast | EMA快线周期 | 9 | 5-15 |
| emaMid | EMA中线周期 | 21 | 15-30 |
| emaSlow | EMA慢线周期 | 55 | 40-100 |
| stopLossMultiplier | 止损ATR倍数 | 2.0 | 1.5-4.0 |
| takeProfitMultiplier1 | 止盈ATR倍数 | 8.0 | 5.0-15.0 |

### 数据源配置

系统支持三种数据源：

1. **database** (推荐): 从本地数据库加载历史数据
2. **exchange**: 从交易所API实时获取数据
3. **csv**: 从CSV文件加载数据

## 📊 结果解读

### 关键指标说明

- **收益率**: 总收益 / 初始资金
- **年化收益率**: 按年计算的收益率
- **最大回撤**: 资金曲线的最大下跌幅度
- **胜率**: 盈利交易数 / 总交易数
- **盈亏比**: 平均盈利 / 平均亏损
- **夏普比率**: 风险调整后收益，>1.0为优秀
- **索提诺比率**: 下行风险调整收益，>1.5为优秀

### 策略表现评估

**优秀策略标准**:
- 年化收益率 > 20%
- 最大回撤 < 15%
- 胜率 > 55%
- 夏普比率 > 1.0
- 盈亏比 > 1.5

## 🔍 故障排除

### 常见问题

1. **数据加载失败**
   ```
   错误: 未能加载历史数据，回测中止
   解决: 检查数据库连接，确保有足够的历史数据
   ```

2. **内存不足**
   ```
   错误: OutOfMemoryError
   解决: 增加JVM内存 -Xmx4g 或减少回测时间范围
   ```

3. **编译错误**
   ```
   错误: 找不到主类
   解决: 先运行 mvn compile 编译项目
   ```

### 性能优化建议

1. **数据量控制**: 回测时间不超过1年
2. **并发控制**: 参数优化时合理设置线程数
3. **内存管理**: 定期清理缓存数据
4. **数据库优化**: 为时间戳字段建立索引

## 📝 自定义配置

### 创建自定义回测

```java
public void runCustomBacktest() {
    BacktestConfig config = BacktestConfig.builder()
        .startDate(LocalDateTime.of(2024, 1, 1, 0, 0))
        .endDate(LocalDateTime.of(2024, 6, 30, 23, 59))
        .symbols(Arrays.asList("BTC-USDT-SWAP"))
        .initialCapital(50000.0)  // 5万USDT
        .positionSizeRatio(0.05)  // 5%仓位
        .mainTimeframe("1h")      // 1小时周期
        .emaFast(12)             // 自定义EMA参数
        .emaMid(26)
        .emaSlow(50)
        .stopLossMultiplier(2.5)  // 自定义止损
        .takeProfitMultiplier1(10.0)
        .tradeFeeRate(0.0005)     // 0.05%手续费
        .build();

    EMARetraceStrategyBacktest backtest = new EMARetraceStrategyBacktest();
    BacktestResult result = backtest.runBacktest(config);
    
    // 处理结果...
}
```

## 🎯 最佳实践

1. **先快速测试**: 使用quick模式验证系统正常
2. **参数优化**: 使用optimization模式找最优参数
3. **完整回测**: 使用single模式进行最终验证
4. **多时间段验证**: 在不同市场环境下测试策略
5. **风险控制**: 关注最大回撤，设置合理止损

## 📞 技术支持

如果遇到问题，请检查：
1. 日志输出中的错误信息
2. 数据库连接状态
3. 系统内存使用情况
4. 网络连接（如使用exchange数据源）

更多技术细节请参考项目文档和源码注释。
