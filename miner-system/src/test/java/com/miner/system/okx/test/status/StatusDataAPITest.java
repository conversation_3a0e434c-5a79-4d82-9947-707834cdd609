package com.miner.system.okx.test.status;


import com.alibaba.fastjson.JSONObject;
import com.miner.system.okx.service.status.StatusDataAPIService;
import com.miner.system.okx.service.status.impl.StatusDataAPIServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class StatusDataAPITest extends StatusDataAPIBaseTest {

    private static final Logger LOG = LoggerFactory.getLogger(StatusDataAPITest.class);
    private StatusDataAPIService statusDataAPIService;

    @Before
    public void before() {
        config = config();
        statusDataAPIService = new StatusDataAPIServiceImpl(config);
    }


    /**
     * status
     * GET /api/v5/system/status
     */
    @Test
    public void testGetStatus() {
        JSONObject result = statusDataAPIService.getStatus(null);
        this.toResultString(StatusDataAPITest.LOG, "result", result);

    }


}
